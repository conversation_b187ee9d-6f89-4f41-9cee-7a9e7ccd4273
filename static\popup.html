<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Companion</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            width: 400px;
            height: 600px;
            overflow: hidden;
        }
        * {
            box-sizing: border-box;
        }
        #app {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="app"></div>
    <script type="module">
        // Simple popup implementation
        const app = document.getElementById('app');
        
        app.innerHTML = `
            <div style="background: #f9fafb; height: 100%; display: flex; flex-direction: column;">
                <header style="background: white; padding: 16px 20px; border-bottom: 1px solid #e5e7eb;">
                    <h1 style="margin: 0; font-size: 18px; font-weight: 600; color: #111827;">Website Companion</h1>
                    <p style="margin: 4px 0 0 0; font-size: 12px; color: #6b7280;" id="current-site">Loading...</p>
                </header>
                
                <div style="padding: 16px 20px; background: white; border-bottom: 1px solid #e5e7eb;">
                    <input type="text" placeholder="Search notes and tasks..." style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;" id="search-input">
                </div>
                
                <div style="padding: 16px 20px; background: white; border-bottom: 1px solid #e5e7eb;">
                    <button style="width: 100%; padding: 10px; background: #3b82f6; color: white; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer;" onclick="openSidePanel()">
                        Open Side Panel
                    </button>
                </div>
                
                <main style="flex: 1; padding: 16px 20px; text-align: center; display: flex; align-items: center; justify-content: center;">
                    <div>
                        <p style="color: #6b7280; margin: 0 0 16px 0;">Click "Open Side Panel" for the full experience</p>
                        <p style="font-size: 12px; color: #9ca3af;">The side panel provides persistent access to your notes and tasks</p>
                    </div>
                </main>
            </div>
        `;
        
        // Get current tab info
        async function getCurrentTab() {
            try {
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                return tabs[0];
            } catch (error) {
                console.error('Failed to get current tab:', error);
                return null;
            }
        }
        
        // Update current site display
        async function updateCurrentSite() {
            const tab = await getCurrentTab();
            const siteElement = document.getElementById('current-site');
            if (tab && tab.url) {
                try {
                    const url = new URL(tab.url);
                    const domain = url.hostname.replace(/^www\./, '');
                    siteElement.textContent = `Current site: ${domain}`;
                } catch (error) {
                    siteElement.textContent = 'Current site: Unknown';
                }
            } else {
                siteElement.textContent = 'Current site: Unknown';
            }
        }
        
        // Open side panel
        window.openSidePanel = async function() {
            try {
                const tab = await getCurrentTab();
                if (tab) {
                    await chrome.sidePanel.open({ tabId: tab.id });
                    window.close();
                }
            } catch (error) {
                console.error('Failed to open side panel:', error);
                alert('Failed to open side panel. Please try again.');
            }
        };
        
        // Initialize
        updateCurrentSite();
    </script>
</body>
</html>
