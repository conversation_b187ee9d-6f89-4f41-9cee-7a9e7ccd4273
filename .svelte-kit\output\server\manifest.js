export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png"]),
	mimeTypes: {".png":"image/png"},
	_: {
		client: {start:"_app/immutable/entry/start.B8LavZ9_.js",app:"_app/immutable/entry/app.C4JISAXU.js",imports:["_app/immutable/entry/start.B8LavZ9_.js","_app/immutable/chunks/BkpnSdVU.js","_app/immutable/chunks/DqHgutWf.js","_app/immutable/chunks/DEldJQSp.js","_app/immutable/entry/app.C4JISAXU.js","_app/immutable/chunks/DqHgutWf.js","_app/immutable/chunks/CBEuTvut.js","_app/immutable/chunks/DpgcOpH8.js","_app/immutable/chunks/DEldJQSp.js","_app/immutable/chunks/CinGyS9S.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js'))
		],
		routes: [
			
		],
		prerendered_routes: new Set(["/","/popup","/sidepanel"]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
