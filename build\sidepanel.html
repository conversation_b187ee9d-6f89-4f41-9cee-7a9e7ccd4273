<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Companion - Side Panel</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            background: #f9fafb;
        }
        * {
            box-sizing: border-box;
        }
        .app {
            max-width: 400px;
            margin: 0 auto;
            background: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .app-header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .app-header h1 {
            margin: 0 0 4px 0;
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }
        .current-site {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }
        .search-section {
            padding: 16px 20px;
            background: white;
            border-bottom: 1px solid #e5e7eb;
        }
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #e5e7eb;
        }
        .tab {
            flex: 1;
            padding: 12px 8px;
            border: none;
            background: none;
            color: #6b7280;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        .tab:hover {
            color: #374151;
        }
        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }
        .action-buttons {
            padding: 16px 20px;
            background: white;
            border-bottom: 1px solid #e5e7eb;
        }
        .add-btn {
            width: 100%;
            padding: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        .add-btn:hover {
            background: #2563eb;
        }
        .content {
            flex: 1;
            padding: 16px 20px;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }
        .empty-state p {
            margin: 0 0 16px 0;
        }
        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="app-header">
            <h1>Website Companion</h1>
            <p class="current-site" id="current-site">Loading...</p>
        </header>
        
        <div class="search-section">
            <input type="text" placeholder="Search notes and tasks..." class="search-input" id="search-input">
        </div>
        
        <nav class="tabs">
            <button class="tab active" data-tab="tasks">Active Tasks</button>
            <button class="tab" data-tab="notes">Notes</button>
            <button class="tab" data-tab="completed">Completed</button>
        </nav>
        
        <div class="action-buttons">
            <button class="add-btn" id="add-btn">+ Add Task</button>
        </div>
        
        <main class="content">
            <div class="loading">
                <p>Loading your notes and tasks...</p>
                <p style="font-size: 12px;">This is a simplified version. The full SvelteKit app will be loaded here.</p>
            </div>
        </main>
    </div>

    <script type="module">
        // Simple side panel implementation
        let currentTab = 'tasks';
        let currentDomain = null;
        
        // Get current tab info
        async function getCurrentTab() {
            try {
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                return tabs[0];
            } catch (error) {
                console.error('Failed to get current tab:', error);
                return null;
            }
        }
        
        // Update current site display
        async function updateCurrentSite() {
            const tab = await getCurrentTab();
            const siteElement = document.getElementById('current-site');
            if (tab && tab.url) {
                try {
                    const url = new URL(tab.url);
                    const domain = url.hostname.replace(/^www\./, '');
                    currentDomain = domain;
                    siteElement.textContent = `Current site: ${domain}`;
                } catch (error) {
                    siteElement.textContent = 'Current site: Unknown';
                }
            } else {
                siteElement.textContent = 'Current site: Unknown';
            }
        }
        
        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                currentTab = tab.dataset.tab;
                
                const addBtn = document.getElementById('add-btn');
                if (currentTab === 'notes') {
                    addBtn.textContent = '+ Add Note';
                } else {
                    addBtn.textContent = '+ Add Task';
                }
            });
        });
        
        // Add button
        document.getElementById('add-btn').addEventListener('click', () => {
            alert(`This would open a modal to add a new ${currentTab === 'notes' ? 'note' : 'task'} for ${currentDomain || 'global'}`);
        });
        
        // Listen for tab changes
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message) => {
                if (message.type === 'TAB_CHANGED' || message.type === 'URL_CHANGED') {
                    updateCurrentSite();
                }
            });
        }
        
        // Initialize
        updateCurrentSite();
    </script>
</body>
</html>
