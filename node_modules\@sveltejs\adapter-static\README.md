# @sveltejs/adapter-static

[Adapter](https://svelte.dev/docs/kit/adapters) for SvelteKit apps that prerenders your entire site as a collection of static files. It's also possible to create an SPA with it by specifying a fallback page which renders an empty shell. If you'd like to prerender only some pages and not create an SPA for those left out, you will need to use a different adapter together with [the `prerender` option](https://svelte.dev/docs/kit/page-options#prerender).

## Docs

[Docs](https://svelte.dev/docs/kit/adapter-static)

## Changelog

[The Changelog for this package is available on GitHub](https://github.com/sveltejs/kit/blob/main/packages/adapter-static/CHANGELOG.md).

## License

[MIT](LICENSE)
