<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="./favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		
		<link rel="modulepreload" href="./_app/immutable/entry/start.B8LavZ9_.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/BkpnSdVU.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/DqHgutWf.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/DEldJQSp.js">
		<link rel="modulepreload" href="./_app/immutable/entry/app.C4JISAXU.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/CBEuTvut.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/DpgcOpH8.js">
		<link rel="modulepreload" href="./_app/immutable/chunks/CinGyS9S.js">
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">
			<script>
				{
					__sveltekit_og9wdx = {
						base: new URL(".", location).pathname.slice(0, -1)
					};

					const element = document.currentScript.parentElement;

					Promise.all([
						import("./_app/immutable/entry/start.B8LavZ9_.js"),
						import("./_app/immutable/entry/app.C4JISAXU.js")
					]).then(([kit, app]) => {
						kit.start(app, element);
					});
				}
			</script>
		</div>
	</body>
</html>
