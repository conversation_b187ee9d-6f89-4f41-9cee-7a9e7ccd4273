import{c as s,a as c}from"../chunks/DpgcOpH8.js";import{i,E as p,j as f,k as l,l as u,m as d,o as m,p as _}from"../chunks/DqHgutWf.js";function v(a,n,...e){var r=a,o=l,t;i(()=>{o!==(o=n())&&(t&&(u(t),t=null),t=f(()=>o(r,...e)))},p),d&&(r=m)}const y=!0,h=!1,b=!0,T=Object.freeze(Object.defineProperty({__proto__:null,csr:b,prerender:y,ssr:h},Symbol.toStringTag,{value:"Module"}));function j(a,n){var e=s(),r=_(e);v(r,()=>n.children),c(a,e)}export{j as component,T as universal};
