import{m as h,a3 as A,a8 as M,a9 as m,aa as T,ab as P,a5 as H,J as Y,ac as B,K as F,a1 as $,a2 as x,i as J,ad as K,H as I,ae as N,D as w,C as L,o as _,af as R,ag as D,y as z,ah as G,ai as Q,aj as U,ak as X,al as Z,j as ee,q as te,c as re,s as ae}from"./DqHgutWf.js";import{b as ne}from"./DpgcOpH8.js";const ie=["touchstart","touchmove"];function se(e){return ie.includes(e)}function he(e){h&&A(e)!==null&&M(e)}let O=!1;function oe(){O||(O=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function V(e){var t=P,a=H;m(null),T(null);try{return e()}finally{m(t),T(a)}}function ve(e,t,a,n=a){e.addEventListener(t,()=>V(a));const i=e.__on_r;i?e.__on_r=()=>{i(),n(!0)}:e.__on_r=()=>n(!0),oe()}const ue=new Set,C=new Set;function le(e,t,a,n={}){function i(r){if(n.capture||E.call(t,r),!r.cancelBubble)return V(()=>a?.call(this,r))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?B(()=>{t.addEventListener(e,i,n)}):t.addEventListener(e,i,n),i}function pe(e,t,a,n,i){var r={capture:n,passive:i},l=le(e,t,a,r);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&Y(()=>{t.removeEventListener(e,l,r)})}function E(e){var t=this,a=t.ownerDocument,n=e.type,i=e.composedPath?.()||[],r=i[0]||e.target,l=0,v=e.__root;if(v){var d=i.indexOf(v);if(d!==-1&&(t===document||t===window)){e.__root=t;return}var p=i.indexOf(t);if(p===-1)return;d<=p&&(l=d)}if(r=i[l]||e.target,r!==t){F(e,"currentTarget",{configurable:!0,get(){return r||a}});var S=P,f=H;m(null),T(null);try{for(var s,o=[];r!==null;){var y=r.assignedSlot||r.parentNode||r.host||null;try{var c=r["__"+n];if(c!=null&&(!r.disabled||e.target===r))if($(c)){var[q,...W]=c;q.apply(r,[e,...W])}else c.call(r,e)}catch(b){s?o.push(b):s=b}if(e.cancelBubble||y===t||y===null)break;r=y}if(s){for(let b of o)queueMicrotask(()=>{throw b});throw s}}finally{e.__root=t,delete e.currentTarget,m(S),T(f)}}}let u;function fe(){u=void 0}function ye(e){let t=null,a=h;var n;if(h){for(t=_,u===void 0&&(u=A(document.head));u!==null&&(u.nodeType!==8||u.data!==I);)u=N(u);u===null?w(!1):u=L(N(u))}h||(n=document.head.appendChild(x()));try{J(()=>e(n),K)}finally{a&&(w(!0),u=_,L(t))}}function ge(e,t){var a=t==null?"":typeof t=="object"?t+"":t;a!==(e.__t??=e.nodeValue)&&(e.__t=a,e.nodeValue=a+"")}function de(e,t){return j(e,t)}function we(e,t){R(),t.intro=t.intro??!1;const a=t.target,n=h,i=_;try{for(var r=A(a);r&&(r.nodeType!==8||r.data!==I);)r=N(r);if(!r)throw D;w(!0),L(r),z();const l=j(e,{...t,anchor:r});if(_===null||_.nodeType!==8||_.data!==G)throw Q(),D;return w(!1),l}catch(l){if(l===D)return t.recover===!1&&U(),R(),M(a),w(!1),de(e,t);throw l}finally{w(n),L(i),fe()}}const g=new Map;function j(e,{target:t,anchor:a,props:n={},events:i,context:r,intro:l=!0}){R();var v=new Set,d=f=>{for(var s=0;s<f.length;s++){var o=f[s];if(!v.has(o)){v.add(o);var y=se(o);t.addEventListener(o,E,{passive:y});var c=g.get(o);c===void 0?(document.addEventListener(o,E,{passive:y}),g.set(o,1)):g.set(o,c+1)}}};d(X(ue)),C.add(d);var p=void 0,S=Z(()=>{var f=a??t.appendChild(x());return ee(()=>{if(r){te({});var s=re;s.c=r}i&&(n.$$events=i),h&&ne(f,null),p=e(f,n)||{},h&&(H.nodes_end=_),r&&ae()}),()=>{for(var s of v){t.removeEventListener(s,E);var o=g.get(s);--o===0?(document.removeEventListener(s,E),g.delete(s)):g.set(s,o)}C.delete(d),f!==a&&f.parentNode?.removeChild(f)}});return k.set(p,S),p}let k=new WeakMap;function Ee(e,t){const a=k.get(e);return a?(k.delete(e),a(t)):Promise.resolve()}export{we as a,oe as b,pe as e,ye as h,ve as l,de as m,he as r,ge as s,Ee as u};
