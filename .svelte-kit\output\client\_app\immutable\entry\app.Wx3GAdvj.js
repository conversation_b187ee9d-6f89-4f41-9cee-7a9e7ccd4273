const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.DiOofISJ.js","../chunks/DpgcOpH8.js","../chunks/DqHgutWf.js","../nodes/1.bxIgOi01.js","../chunks/C1sE6Cnk.js","../chunks/CBEuTvut.js","../chunks/D-Tr_g7H.js","../chunks/DPUWE-4f.js","../chunks/DEldJQSp.js","../nodes/2.DwOJIi0O.js","../nodes/3.81liJh8l.js","../chunks/Cw7DmGe3.js","../chunks/CinGyS9S.js","../assets/App.BRvRBMPF.css","../assets/3.V2J_20dn.css","../nodes/4.D-Tal-vB.js","../assets/4.CU7t7kQJ.css"])))=>i.map(i=>d[i]);
import{m as q,y as U,i as Y,E as K,j as Q,G as W,o as z,am as H,an as J,b as X,ac as Z,S as $,L as p,Q as tt,g as m,ao as et,K as rt,I as st,q as at,u as nt,a as ot,ap as x,aq as it,p as S,x as ct,s as ut,v as lt,w as ft,ar as L,t as dt}from"../chunks/DqHgutWf.js";import{a as mt,m as ht,u as _t,s as vt}from"../chunks/CBEuTvut.js";import{f as V,a as y,c as O,t as gt}from"../chunks/DpgcOpH8.js";import{o as yt}from"../chunks/DEldJQSp.js";import{p as A,i as T}from"../chunks/CinGyS9S.js";function C(s,t,a){q&&U();var i=s,n,e;Y(()=>{n!==(n=t())&&(e&&(W(e),e=null),n&&(e=Q(()=>a(i,n))))},K),q&&(i=z)}function D(s,t){return s===t||s?.[$]===t}function j(s={},t,a,i){return H(()=>{var n,e;return J(()=>{n=e,e=[],X(()=>{s!==a(...e)&&(t(s,...e),n&&D(a(...n),s)&&t(null,...n))})}),()=>{Z(()=>{e&&D(a(...e),s)&&t(null,...e)})}}),s}function Et(s){return class extends bt{constructor(t){super({component:s,...t})}}}class bt{#e;#t;constructor(t){var a=new Map,i=(e,r)=>{var o=st(r);return a.set(e,o),o};const n=new Proxy({...t.props||{},$$events:{}},{get(e,r){return m(a.get(r)??i(r,Reflect.get(e,r)))},has(e,r){return r===tt?!0:(m(a.get(r)??i(r,Reflect.get(e,r))),Reflect.has(e,r))},set(e,r,o){return p(a.get(r)??i(r,o),o),Reflect.set(e,r,o)}});this.#t=(t.hydrate?mt:ht)(t.component,{target:t.target,anchor:t.anchor,props:n,context:t.context,intro:t.intro??!1,recover:t.recover}),(!t?.props?.$$host||t.sync===!1)&&et(),this.#e=n.$$events;for(const e of Object.keys(this.#t))e==="$set"||e==="$destroy"||e==="$on"||rt(this,e,{get(){return this.#t[e]},set(r){this.#t[e]=r},enumerable:!0});this.#t.$set=e=>{Object.assign(n,e)},this.#t.$destroy=()=>{_t(this.#t)}}$set(t){this.#t.$set(t)}$on(t,a){this.#e[t]=this.#e[t]||[];const i=(...n)=>a.call(this,...n);return this.#e[t].push(i),()=>{this.#e[t]=this.#e[t].filter(n=>n!==i)}}$destroy(){this.#t.$destroy()}}const Pt="modulepreload",Rt=function(s,t){return new URL(s,t).href},N={},k=function(t,a,i){let n=Promise.resolve();if(a&&a.length>0){let r=function(u){return Promise.all(u.map(f=>Promise.resolve(f).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};const o=document.getElementsByTagName("link"),w=document.querySelector("meta[property=csp-nonce]"),E=w?.nonce||w?.getAttribute("nonce");n=r(a.map(u=>{if(u=Rt(u,i),u in N)return;N[u]=!0;const f=u.endsWith(".css"),h=f?'[rel="stylesheet"]':"";if(i)for(let _=o.length-1;_>=0;_--){const c=o[_];if(c.href===u&&(!f||c.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${h}`))return;const l=document.createElement("link");if(l.rel=f?"stylesheet":Pt,f||(l.as="script"),l.crossOrigin="",l.href=u,E&&l.setAttribute("nonce",E),document.head.appendChild(l),f)return new Promise((_,c)=>{l.addEventListener("load",_),l.addEventListener("error",()=>c(new Error(`Unable to preload CSS for ${u}`)))})}))}function e(r){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=r,window.dispatchEvent(o),!o.defaultPrevented)throw r}return n.then(r=>{for(const o of r||[])o.status==="rejected"&&e(o.reason);return t().catch(e)})},It={};var kt=V('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),wt=V("<!> <!>",1);function St(s,t){at(t,!0);let a=A(t,"components",23,()=>[]),i=A(t,"data_0",3,null),n=A(t,"data_1",3,null);nt(()=>t.stores.page.set(t.page)),ot(()=>{t.stores,t.page,t.constructors,a(),t.form,i(),n(),t.stores.page.notify()});let e=x(!1),r=x(!1),o=x(null);yt(()=>{const c=t.stores.page.subscribe(()=>{m(e)&&(p(r,!0),it().then(()=>{p(o,document.title||"untitled page",!0)}))});return p(e,!0),c});const w=L(()=>t.constructors[1]);var E=wt(),u=S(E);{var f=c=>{var d=O();const b=L(()=>t.constructors[0]);var P=S(d);C(P,()=>m(b),(v,g)=>{j(g(v,{get data(){return i()},get form(){return t.form},children:(R,Lt)=>{var I=O(),M=S(I);C(M,()=>m(w),(B,F)=>{j(F(B,{get data(){return n()},get form(){return t.form}}),G=>a()[1]=G,()=>a()?.[1])}),y(R,I)},$$slots:{default:!0}}),R=>a()[0]=R,()=>a()?.[0])}),y(c,d)},h=c=>{var d=O();const b=L(()=>t.constructors[0]);var P=S(d);C(P,()=>m(b),(v,g)=>{j(g(v,{get data(){return i()},get form(){return t.form}}),R=>a()[0]=R,()=>a()?.[0])}),y(c,d)};T(u,c=>{t.constructors[1]?c(f):c(h,!1)})}var l=ct(u,2);{var _=c=>{var d=kt(),b=lt(d);{var P=v=>{var g=gt();dt(()=>vt(g,m(o))),y(v,g)};T(b,v=>{m(r)&&v(P)})}ft(d),y(c,d)};T(l,c=>{m(e)&&c(_)})}y(s,E),ut()}const qt=Et(St),Dt=[()=>k(()=>import("../nodes/0.DiOofISJ.js"),__vite__mapDeps([0,1,2]),import.meta.url),()=>k(()=>import("../nodes/1.bxIgOi01.js"),__vite__mapDeps([3,1,2,4,5,6,7,8]),import.meta.url),()=>k(()=>import("../nodes/2.DwOJIi0O.js"),__vite__mapDeps([9,1,2,4]),import.meta.url),()=>k(()=>import("../nodes/3.81liJh8l.js"),__vite__mapDeps([10,1,2,4,5,11,8,12,6,13,14]),import.meta.url),()=>k(()=>import("../nodes/4.D-Tal-vB.js"),__vite__mapDeps([15,1,2,4,5,11,8,12,6,13,16]),import.meta.url)],Nt=[],Vt={"/":[2],"/popup":[3],"/sidepanel":[4]},pt={handleError:({error:s})=>{console.error(s)},reroute:()=>{},transport:{}},xt=Object.fromEntries(Object.entries(pt.transport).map(([s,t])=>[s,t.decode])),Mt=!1,Bt=(s,t)=>xt[s](t);export{Bt as decode,xt as decoders,Vt as dictionary,Mt as hash,pt as hooks,It as matchers,Dt as nodes,qt as root,Nt as server_loads};
