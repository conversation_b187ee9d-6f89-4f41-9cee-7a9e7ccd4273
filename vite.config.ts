import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit()],
	build: {
		target: 'esnext',
		rollupOptions: {
			output: {
				// Ensure consistent file names for Chrome extension
				entryFileNames: '[name].js',
				chunkFileNames: '[name].js',
				assetFileNames: '[name].[ext]',
				// Reduce chunk splitting for Chrome extensions
				manualChunks: undefined
			}
		}
	},
	define: {
		// Ensure chrome API is available
		global: 'globalThis'
	}
});
