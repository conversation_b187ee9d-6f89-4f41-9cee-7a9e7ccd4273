import{f as n,a as i}from"../chunks/DpgcOpH8.js";import"../chunks/C1sE6Cnk.js";import{n as t}from"../chunks/DqHgutWf.js";var a=n("<h1>Contextual Website Companion</h1> <p>This is a Chrome extension for managing notes and tasks specific to websites.</p> <p>To use this extension:</p> <ol><li>Build the extension: <code>bun run build</code></li> <li>Load the <code>build</code> folder as an unpacked extension in Chrome</li> <li>Use the side panel or popup to manage your notes and tasks</li></ol>",1);function l(e){var o=a();t(6),i(e,o)}export{l as component};
