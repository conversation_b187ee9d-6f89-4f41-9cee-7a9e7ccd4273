{"name": "svelte-check", "description": "Svelte Code Checker Terminal Interface", "version": "4.2.1", "main": "./dist/src/index.js", "bin": "./bin/svelte-check", "author": "The Svelte Community", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sveltejs/language-tools.git"}, "keywords": ["svelte", "cli"], "files": ["bin", "dist"], "bugs": {"url": "https://github.com/sveltejs/language-tools/issues"}, "homepage": "https://github.com/sveltejs/language-tools#readme", "engines": {"node": ">= 18.0.0"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "chokidar": "^4.0.1", "fdir": "^6.2.0", "picocolors": "^1.0.0", "sade": "^1.7.4"}, "peerDependencies": {"svelte": "^4.0.0 || ^5.0.0-next.0", "typescript": ">=5.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-replace": "5.0.2", "@rollup/plugin-typescript": "^10.0.0", "@types/sade": "^1.7.2", "builtin-modules": "^3.3.0", "rollup": "3.7.5", "rollup-plugin-cleanup": "^3.2.0", "rollup-plugin-copy": "^3.4.0", "svelte": "^4.2.19", "typescript": "^5.8.2", "vscode-languageserver": "8.0.2", "vscode-languageserver-protocol": "3.17.2", "vscode-languageserver-types": "3.17.2", "vscode-uri": "~3.1.0", "svelte-language-server": "0.17.0"}, "scripts": {"build": "rollup -c && node ./dist/src/index.js --workspace ./test --tsconfig ./tsconfig.json", "test": "npm run build"}}