export interface BaseItem {
  id: string;
  content: string;
  details?: string;
  domain: string | null; // null for global items
  createdAt: string;
  tags: string[];
}

export interface Note extends BaseItem {
  type: 'note';
}

export interface Task extends BaseItem {
  type: 'task';
  completed: boolean;
  dueDate?: string | null;
}

export type Item = Note | Task;

export interface FilterState {
  showGlobal: boolean;
  showContextual: boolean;
  searchTerm: string;
}

export interface ModalState {
  isOpen: boolean;
  type: 'add-note' | 'add-task' | 'edit-note' | 'edit-task' | 'delete-confirm' | null;
  item: Item | null;
}

export interface TabInfo {
  id?: number;
  url?: string;
  title?: string;
}

export interface ChromeMessage {
  type: 'TAB_CHANGED' | 'URL_CHANGED' | 'GET_CURRENT_TAB';
  tabId?: number;
  url?: string;
  tab?: TabInfo;
}
