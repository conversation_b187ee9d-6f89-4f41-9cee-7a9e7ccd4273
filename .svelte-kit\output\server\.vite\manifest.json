{".svelte-kit/generated/server/internal.js": {"file": "internal.js", "name": "internal", "src": ".svelte-kit/generated/server/internal.js", "isEntry": true, "imports": ["_internal.js"]}, "_App.BRvRBMPF.css": {"file": "_app/immutable/assets/App.BRvRBMPF.css", "src": "_App.BRvRBMPF.css"}, "_App.js": {"file": "chunks/App.js", "name": "App", "imports": ["_index.js", "_index2.js"], "css": ["_app/immutable/assets/App.BRvRBMPF.css"]}, "_exports.js": {"file": "chunks/exports.js", "name": "exports"}, "_index.js": {"file": "chunks/index.js", "name": "index"}, "_index2.js": {"file": "chunks/index2.js", "name": "index", "imports": ["_index.js"]}, "_internal.js": {"file": "chunks/internal.js", "name": "internal", "imports": ["_index.js"]}, "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte": {"file": "entries/fallbacks/error.svelte.js", "name": "entries/fallbacks/error.svelte", "src": "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/error.svelte", "isEntry": true, "imports": ["_index.js", "_exports.js", "_index2.js"]}, "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/layout.svelte": {"file": "entries/fallbacks/layout.svelte.js", "name": "entries/fallbacks/layout.svelte", "src": "node_modules/@sveltejs/kit/src/runtime/components/svelte-5/layout.svelte", "isEntry": true}, "node_modules/@sveltejs/kit/src/runtime/server/index.js": {"file": "index.js", "name": "index", "src": "node_modules/@sveltejs/kit/src/runtime/server/index.js", "isEntry": true, "imports": ["_index.js", "_internal.js", "_exports.js", "_index2.js"]}, "src/routes/+layout.ts": {"file": "entries/pages/_layout.ts.js", "name": "entries/pages/_layout.ts", "src": "src/routes/+layout.ts", "isEntry": true}, "src/routes/+page.svelte": {"file": "entries/pages/_page.svelte.js", "name": "entries/pages/_page.svelte", "src": "src/routes/+page.svelte", "isEntry": true}, "src/routes/popup/+page.svelte": {"file": "entries/pages/popup/_page.svelte.js", "name": "entries/pages/popup/_page.svelte", "src": "src/routes/popup/+page.svelte", "isEntry": true, "imports": ["_index.js", "_App.js"], "css": ["_app/immutable/assets/_page.V2J_20dn.css"]}, "src/routes/sidepanel/+page.svelte": {"file": "entries/pages/sidepanel/_page.svelte.js", "name": "entries/pages/sidepanel/_page.svelte", "src": "src/routes/sidepanel/+page.svelte", "isEntry": true, "imports": ["_index.js", "_App.js"], "css": ["_app/immutable/assets/_page.CU7t7kQJ.css"]}}