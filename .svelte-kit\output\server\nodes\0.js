

export const index = 0;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export const universal = {
  "prerender": true,
  "ssr": false,
  "csr": true
};
export const universal_id = "src/routes/+layout.ts";
export const imports = ["_app/immutable/nodes/0.DiOofISJ.js","_app/immutable/chunks/DpgcOpH8.js","_app/immutable/chunks/DqHgutWf.js"];
export const stylesheets = [];
export const fonts = [];
