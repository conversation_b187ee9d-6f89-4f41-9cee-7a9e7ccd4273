import{ap as T,g as x,L as P,aq as pt}from"./DqHgutWf.js";import{w as Ae,o as Ne}from"./DEldJQSp.js";new URL("sveltekit-internal://");function gt(e,t){return e==="/"||t==="ignore"?e:t==="never"?e.endsWith("/")?e.slice(0,-1):e:t==="always"&&!e.endsWith("/")?e+"/":e}function mt(e){return e.split("%25").map(decodeURI).join("%25")}function _t(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function ge({href:e}){return e.split("#")[0]}function wt(e,t,n,r=!1){const a=new URL(e);Object.defineProperty(a,"searchParams",{value:new Proxy(a.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return f=>(n(f),i[o](f));t();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];r&&s.push("hash");for(const i of s)Object.defineProperty(a,i,{get(){return t(),e[i]},enumerable:!0,configurable:!0});return a}function yt(...e){let t=5381;for(const n of e)if(typeof n=="string"){let r=n.length;for(;r;)t=t*33^n.charCodeAt(--r)}else if(ArrayBuffer.isView(n)){const r=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let a=r.length;for(;a;)t=t*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(t>>>0).toString(36)}function vt(e){const t=atob(e),n=new Uint8Array(t.length);for(let r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return n.buffer}const bt=window.fetch;window.fetch=(e,t)=>((e instanceof Request?e.method:t?.method||"GET")!=="GET"&&G.delete(ke(e)),bt(e,t));const G=new Map;function At(e,t){const n=ke(e,t),r=document.querySelector(n);if(r?.textContent){let{body:a,...s}=JSON.parse(r.textContent);const i=r.getAttribute("data-ttl");return i&&G.set(n,{body:a,init:s,ttl:1e3*Number(i)}),r.getAttribute("data-b64")!==null&&(a=vt(a)),Promise.resolve(new Response(a,s))}return window.fetch(e,t)}function kt(e,t,n){if(G.size>0){const r=ke(e,n),a=G.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(n?.cache))return new Response(a.body,a.init);G.delete(r)}}return window.fetch(t,n)}function ke(e,t){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(t?.headers||t?.body){const a=[];t.headers&&a.push([...new Headers(t.headers)].join(",")),t.body&&(typeof t.body=="string"||ArrayBuffer.isView(t.body))&&a.push(t.body),r+=`[data-hash="${yt(...a)}"]`}return r}const St=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function Et(e){const t=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${It(e).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return t.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(s)return t.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,f)=>{if(f%2){if(c.startsWith("x+"))return me(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return me(String.fromCharCode(...c.slice(2).split("-").map(_=>parseInt(_,16))));const d=St.exec(c),[,h,u,l,p]=d;return t.push({name:l,matcher:p,optional:!!h,rest:!!u,chained:u?f===1&&i[0]==="":!1}),u?"(.*?)":h?"([^/]*)?":"([^/]+?)"}return me(c)}).join("")}).join("")}/?$`),params:t}}function Rt(e){return!/^\([^)]+\)$/.test(e)}function It(e){return e.slice(1).split("/").filter(Rt)}function Ut(e,t,n){const r={},a=e.slice(1),s=a.filter(o=>o!==void 0);let i=0;for(let o=0;o<t.length;o+=1){const c=t[o];let f=a[o-i];if(c.chained&&c.rest&&i&&(f=a.slice(o-i,o+1).filter(d=>d).join("/"),i=0),f===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||n[c.matcher](f)){r[c.name]=f;const d=t[o+1],h=a[o+1];d&&!d.rest&&d.optional&&h&&c.chained&&(i=0),!d&&!h&&Object.keys(r).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function me(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Lt({nodes:e,server_loads:t,dictionary:n,matchers:r}){const a=new Set(t);return Object.entries(n).map(([o,[c,f,d]])=>{const{pattern:h,params:u}=Et(o),l={id:o,exec:p=>{const _=h.exec(p);if(_)return Ut(_,u,r)},errors:[1,...d||[]].map(p=>e[p]),layouts:[0,...f||[]].map(i),leaf:s(c)};return l.errors.length=l.layouts.length=Math.max(l.errors.length,l.layouts.length),l});function s(o){const c=o<0;return c&&(o=~o),[c,e[o]]}function i(o){return o===void 0?o:[a.has(o),e[o]]}}function He(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}function je(e,t,n=JSON.stringify){const r=n(t);try{sessionStorage[e]=r}catch{}}const U=globalThis.__sveltekit_og9wdx?.base??"",Tt=globalThis.__sveltekit_og9wdx?.assets??U,xt="1748720443639",Ke="sveltekit:snapshot",We="sveltekit:scroll",Ye="sveltekit:states",Pt="sveltekit:pageurl",V="sveltekit:history",Y="sveltekit:navigation",j={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},ce=location.origin;function Je(e){if(e instanceof URL)return e;let t=document.baseURI;if(!t){const n=document.getElementsByTagName("base");t=n.length?n[0].href:document.URL}return new URL(e,t)}function Se(){return{x:pageXOffset,y:pageYOffset}}function F(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const $e={...j,"":j.hover};function ze(e){let t=e.assignedSlot??e.parentNode;return t?.nodeType===11&&(t=t.host),t}function Xe(e,t){for(;e&&e!==t;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=ze(e)}}function ye(e,t,n){let r;try{if(r=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI),n&&r.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";r.hash=`#${o}${r.hash}`}}catch{}const a=e instanceof SVGAElement?e.target.baseVal:e.target,s=!r||!!a||le(r,t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),i=r?.origin===ce&&e.hasAttribute("download");return{url:r,external:s,target:a,download:i}}function ee(e){let t=null,n=null,r=null,a=null,s=null,i=null,o=e;for(;o&&o!==document.documentElement;)r===null&&(r=F(o,"preload-code")),a===null&&(a=F(o,"preload-data")),t===null&&(t=F(o,"keepfocus")),n===null&&(n=F(o,"noscroll")),s===null&&(s=F(o,"reload")),i===null&&(i=F(o,"replacestate")),o=ze(o);function c(f){switch(f){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:$e[r??"off"],preload_data:$e[a??"off"],keepfocus:c(t),noscroll:c(n),reload:c(s),replace_state:c(i)}}function De(e){const t=Ae(e);let n=!0;function r(){n=!0,t.update(i=>i)}function a(i){n=!1,t.set(i)}function s(i){let o;return t.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:r,set:a,subscribe:s}}const Ze={v:()=>{}};function Ct(){const{set:e,subscribe:t}=Ae(!1);let n;async function r(){clearTimeout(n);try{const a=await fetch(`${Tt}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const i=(await a.json()).version!==xt;return i&&(e(!0),Ze.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:t,check:r}}function le(e,t,n){return e.origin!==ce||!e.pathname.startsWith(t)?!0:n?!(e.pathname===t+"/"||e.pathname===t+"/index.html"||e.protocol==="file:"&&e.pathname.replace(/\/[^/]+\.html?$/,"")===t):!1}function _n(e){}function Fe(e){const t=Nt(e),n=new ArrayBuffer(t.length),r=new DataView(n);for(let a=0;a<n.byteLength;a++)r.setUint8(a,t.charCodeAt(a));return n}const Ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Nt(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",n=0,r=0;for(let a=0;a<e.length;a++)n<<=6,n|=Ot.indexOf(e[a]),r+=6,r===24&&(t+=String.fromCharCode((n&16711680)>>16),t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255),n=r=0);return r===12?(n>>=4,t+=String.fromCharCode(n)):r===18&&(n>>=2,t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255)),t}const jt=-1,$t=-2,Dt=-3,Ft=-4,Vt=-5,Bt=-6;function qt(e,t){if(typeof e=="number")return a(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,r=Array(n.length);function a(s,i=!1){if(s===jt)return;if(s===Dt)return NaN;if(s===Ft)return 1/0;if(s===Vt)return-1/0;if(s===Bt)return-0;if(i)throw new Error("Invalid input");if(s in r)return r[s];const o=n[s];if(!o||typeof o!="object")r[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],f=t?.[c];if(f)return r[s]=f(a(o[1]));switch(c){case"Date":r[s]=new Date(o[1]);break;case"Set":const d=new Set;r[s]=d;for(let l=1;l<o.length;l+=1)d.add(a(o[l]));break;case"Map":const h=new Map;r[s]=h;for(let l=1;l<o.length;l+=2)h.set(a(o[l]),a(o[l+1]));break;case"RegExp":r[s]=new RegExp(o[1],o[2]);break;case"Object":r[s]=Object(o[1]);break;case"BigInt":r[s]=BigInt(o[1]);break;case"null":const u=Object.create(null);r[s]=u;for(let l=1;l<o.length;l+=2)u[o[l]]=a(o[l+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const l=globalThis[c],p=o[1],_=Fe(p),m=new l(_);r[s]=m;break}case"ArrayBuffer":{const l=o[1],p=Fe(l);r[s]=p;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);r[s]=c;for(let f=0;f<o.length;f+=1){const d=o[f];d!==$t&&(c[f]=a(d))}}else{const c={};r[s]=c;for(const f in o){const d=o[f];c[f]=a(d)}}return r[s]}return a(0)}const Qe=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...Qe];const Mt=new Set([...Qe]);[...Mt];function Gt(e){return e.filter(t=>t!=null)}class fe{constructor(t,n){this.status=t,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class Ee{constructor(t,n){this.status=t,this.location=n}}class Re extends Error{constructor(t,n,r){super(r),this.status=t,this.text=n}}const Ht="x-sveltekit-invalidated",Kt="x-sveltekit-trailing-slash";function te(e){return e instanceof fe||e instanceof Re?e.status:500}function Wt(e){return e instanceof Re?e.text:"Internal Error"}let S,J,_e;const Yt=Ne.toString().includes("$$")||/function \w+\(\) \{\}/.test(Ne.toString());Yt?(S={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},J={current:null},_e={current:!1}):(S=new class{#e=T({});get data(){return x(this.#e)}set data(t){P(this.#e,t)}#t=T(null);get form(){return x(this.#t)}set form(t){P(this.#t,t)}#n=T(null);get error(){return x(this.#n)}set error(t){P(this.#n,t)}#r=T({});get params(){return x(this.#r)}set params(t){P(this.#r,t)}#a=T({id:null});get route(){return x(this.#a)}set route(t){P(this.#a,t)}#o=T({});get state(){return x(this.#o)}set state(t){P(this.#o,t)}#s=T(-1);get status(){return x(this.#s)}set status(t){P(this.#s,t)}#i=T(new URL("https://example.com"));get url(){return x(this.#i)}set url(t){P(this.#i,t)}},J=new class{#e=T(null);get current(){return x(this.#e)}set current(t){P(this.#e,t)}},_e=new class{#e=T(!1);get current(){return x(this.#e)}set current(t){P(this.#e,t)}},Ze.v=()=>_e.current=!0);function Jt(e){Object.assign(S,e)}const zt="/__data.json",Xt=".html__data.json";function Zt(e){return e.endsWith(".html")?e.replace(/\.html$/,Xt):e.replace(/\/$/,"")+zt}const Qt=new Set(["icon","shortcut icon","apple-touch-icon"]),D=He(We)??{},z=He(Ke)??{},N={url:De({}),page:De({}),navigating:Ae(null),updated:Ct()};function Ie(e){D[e]=Se()}function en(e,t){let n=e+1;for(;D[n];)delete D[n],n+=1;for(n=t+1;z[n];)delete z[n],n+=1}function q(e){return location.href=e.href,new Promise(()=>{})}async function et(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(U||"/");e&&await e.update()}}function Ve(){}let Ue,ve,ne,C,be,v;const re=[],ae=[];let O=null;const Q=new Map,tt=new Set,tn=new Set,H=new Set;let y={branch:[],error:null,url:null},Le=!1,oe=!1,Be=!0,X=!1,M=!1,nt=!1,Te=!1,rt,A,I,$;const K=new Set;async function bn(e,t,n){document.URL!==location.href&&(location.href=location.href),v=e,await e.hooks.init?.(),Ue=Lt(e),C=document.documentElement,be=t,ve=e.nodes[0],ne=e.nodes[1],ve(),ne(),A=history.state?.[V],I=history.state?.[Y],A||(A=I=Date.now(),history.replaceState({...history.state,[V]:A,[Y]:I},""));const r=D[A];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),n?await dn(be,n):await W({type:"enter",url:Je(v.hash?pn(new URL(location.href)):location.href),replace_state:!0}),un()}function nn(){re.length=0,Te=!1}function at(e){ae.some(t=>t?.snapshot)&&(z[e]=ae.map(t=>t?.snapshot?.capture()))}function ot(e){z[e]?.forEach((t,n)=>{ae[n]?.snapshot?.restore(t)})}function qe(){Ie(A),je(We,D),at(I),je(Ke,z)}async function st(e,t,n,r){return W({type:"goto",url:Je(e),keepfocus:t.keepFocus,noscroll:t.noScroll,replace_state:t.replaceState,state:t.state,redirect_count:n,nav_token:r,accept:()=>{t.invalidateAll&&(Te=!0),t.invalidate&&t.invalidate.forEach(fn)}})}async function rn(e){if(e.id!==O?.id){const t={};K.add(t),O={id:e.id,token:t,promise:lt({...e,preload:t}).then(n=>(K.delete(t),n.type==="loaded"&&n.state.error&&(O=null),n))}}return O.promise}async function we(e){const t=(await de(e,!1))?.route;t&&await Promise.all([...t.layouts,t.leaf].map(n=>n?.[1]()))}function it(e,t,n){y=e.state;const r=document.querySelector("style[data-sveltekit]");if(r&&r.remove(),Object.assign(S,e.props.page),rt=new v.root({target:t,props:{...e.props,stores:N,components:ae},hydrate:n,sync:!1}),ot(I),n){const a={from:null,to:{params:y.params,route:{id:y.route?.id??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};H.forEach(s=>s(a))}oe=!0}function se({url:e,params:t,branch:n,status:r,error:a,route:s,form:i}){let o="never";if(U&&(e.pathname===U||e.pathname===U+"/"))o="always";else for(const l of n)l?.slash!==void 0&&(o=l.slash);e.pathname=gt(e.pathname,o),e.search=e.search;const c={type:"loaded",state:{url:e,params:t,branch:n,error:a,route:s},props:{constructors:Gt(n).map(l=>l.node.component),page:Oe(S)}};i!==void 0&&(c.props.form=i);let f={},d=!S,h=0;for(let l=0;l<Math.max(n.length,y.branch.length);l+=1){const p=n[l],_=y.branch[l];p?.data!==_?.data&&(d=!0),p&&(f={...f,...p.data},d&&(c.props[`data_${h}`]=f),h+=1)}return(!y.url||e.href!==y.url.href||y.error!==a||i!==void 0&&i!==S.form||d)&&(c.props.page={error:a,params:t,route:{id:s?.id??null},state:{},status:r,url:new URL(e),form:i??null,data:d?f:S.data}),c}async function xe({loader:e,parent:t,url:n,params:r,route:a,server_data_node:s}){let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},f=await e();if(f.universal?.load){let d=function(...u){for(const l of u){const{href:p}=new URL(l,n);c.dependencies.add(p)}};const h={route:new Proxy(a,{get:(u,l)=>(o&&(c.route=!0),u[l])}),params:new Proxy(r,{get:(u,l)=>(o&&c.params.add(l),u[l])}),data:s?.data??null,url:wt(n,()=>{o&&(c.url=!0)},u=>{o&&c.search_params.add(u)},v.hash),async fetch(u,l){u instanceof Request&&(l={body:u.method==="GET"||u.method==="HEAD"?void 0:await u.blob(),cache:u.cache,credentials:u.credentials,headers:[...u.headers].length?u.headers:void 0,integrity:u.integrity,keepalive:u.keepalive,method:u.method,mode:u.mode,redirect:u.redirect,referrer:u.referrer,referrerPolicy:u.referrerPolicy,signal:u.signal,...l});const{resolved:p,promise:_}=ct(u,l,n);return o&&d(p.href),_},setHeaders:()=>{},depends:d,parent(){return o&&(c.parent=!0),t()},untrack(u){o=!1;try{return u()}finally{o=!0}}};i=await f.universal.load.call(null,h)??null}return{node:f,loader:e,server:s,universal:f.universal?.load?{type:"data",data:i,uses:c}:null,data:i??s?.data??null,slash:f.universal?.trailingSlash??s?.slash}}function ct(e,t,n){let r=e instanceof Request?e.url:e;const a=new URL(r,n);a.origin===n.origin&&(r=a.href.slice(n.origin.length));const s=oe?kt(r,a.href,t):At(r,t);return{resolved:a,promise:s}}function Me(e,t,n,r,a,s){if(Te)return!0;if(!a)return!1;if(a.parent&&e||a.route&&t||a.url&&n)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(s[i]!==y.params[i])return!0;for(const i of a.dependencies)if(re.some(o=>o(new URL(i))))return!0;return!1}function Pe(e,t){return e?.type==="data"?e:e?.type==="skip"?t??null:null}function an(e,t){if(!e)return new Set(t.searchParams.keys());const n=new Set([...e.searchParams.keys(),...t.searchParams.keys()]);for(const r of n){const a=e.searchParams.getAll(r),s=t.searchParams.getAll(r);a.every(i=>s.includes(i))&&s.every(i=>a.includes(i))&&n.delete(r)}return n}function Ge({error:e,url:t,route:n,params:r}){return{type:"loaded",state:{error:e,url:t,route:n,params:r,branch:[]},props:{page:Oe(S),constructors:[]}}}async function lt({id:e,invalidating:t,url:n,params:r,route:a,preload:s}){if(O?.id===e)return K.delete(O.token),O.promise;const{errors:i,layouts:o,leaf:c}=a,f=[...o,c];i.forEach(g=>g?.().catch(()=>{})),f.forEach(g=>g?.[1]().catch(()=>{}));let d=null;const h=y.url?e!==ie(y.url):!1,u=y.route?a.id!==y.route.id:!1,l=an(y.url,n);let p=!1;const _=f.map((g,w)=>{const b=y.branch[w],k=!!g?.[0]&&(b?.loader!==g[1]||Me(p,u,h,l,b.server?.uses,r));return k&&(p=!0),k});if(_.some(Boolean)){try{d=await dt(n,_)}catch(g){const w=await B(g,{url:n,params:r,route:{id:e}});return K.has(s)?Ge({error:w,url:n,params:r,route:a}):ue({status:te(g),error:w,url:n,route:a})}if(d.type==="redirect")return d}const m=d?.nodes;let R=!1;const E=f.map(async(g,w)=>{if(!g)return;const b=y.branch[w],k=m?.[w];if((!k||k.type==="skip")&&g[1]===b?.loader&&!Me(R,u,h,l,b.universal?.uses,r))return b;if(R=!0,k?.type==="error")throw k;return xe({loader:g[1],url:n,params:r,route:a,parent:async()=>{const he={};for(let pe=0;pe<w;pe+=1)Object.assign(he,(await E[pe])?.data);return he},server_data_node:Pe(k===void 0&&g[0]?{type:"skip"}:k??null,g[0]?b?.server:void 0)})});for(const g of E)g.catch(()=>{});const L=[];for(let g=0;g<f.length;g+=1)if(f[g])try{L.push(await E[g])}catch(w){if(w instanceof Ee)return{type:"redirect",location:w.location};if(K.has(s))return Ge({error:await B(w,{params:r,url:n,route:{id:a.id}}),url:n,params:r,route:a});let b=te(w),k;if(m?.includes(w))b=w.status??b,k=w.error;else if(w instanceof fe)k=w.body;else{if(await N.updated.check())return await et(),await q(n);k=await B(w,{params:r,url:n,route:{id:a.id}})}const Z=await on(g,L,i);return Z?se({url:n,params:r,branch:L.slice(0,Z.idx).concat(Z.node),status:b,error:k,route:a}):await ut(n,{id:a.id},k,b)}else L.push(void 0);return se({url:n,params:r,branch:L,status:200,error:null,route:a,form:t?void 0:null})}async function on(e,t,n){for(;e--;)if(n[e]){let r=e;for(;!t[r];)r-=1;try{return{idx:r+1,node:{node:await n[e](),loader:n[e],data:{},server:null,universal:null}}}catch{continue}}}async function ue({status:e,error:t,url:n,route:r}){const a={};let s=null;if(v.server_loads[0]===0)try{const o=await dt(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==ce||n.pathname!==location.pathname||Le)&&await q(n)}try{const o=await xe({loader:ve,url:n,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:Pe(s)}),c={node:await ne(),loader:ne,universal:null,server:null,data:null};return se({url:n,params:a,branch:[o,c],status:e,error:t,route:null})}catch(o){if(o instanceof Ee)return st(new URL(o.location,location.href),{},0);throw o}}async function sn(e){const t=e.href;if(Q.has(t))return Q.get(t);let n;try{const r=(async()=>{let a=await v.hooks.reroute({url:new URL(e),fetch:async(s,i)=>ct(s,i,e).promise})??e;if(typeof a=="string"){const s=new URL(e);v.hash?s.hash=a:s.pathname=a,a=s}return a})();Q.set(t,r),n=await r}catch{Q.delete(t);return}return n}async function de(e,t){if(e&&!le(e,U,v.hash)){const n=await sn(e);if(!n)return;const r=cn(n);for(const a of Ue){const s=a.exec(r);if(s)return{id:ie(e),invalidating:t,route:a,params:_t(s),url:e}}}}function cn(e){return mt(v.hash?e.hash.replace(/^#/,"").replace(/[?#].+/,""):e.pathname.slice(U.length))||"/"}function ie(e){return(v.hash?e.hash.replace(/^#/,""):e.pathname)+e.search}function ft({url:e,type:t,intent:n,delta:r}){let a=!1;const s=Ce(y,n,e,t);r!==void 0&&(s.navigation.delta=r);const i={...s.navigation,cancel:()=>{a=!0,s.reject(new Error("navigation cancelled"))}};return X||tt.forEach(o=>o(i)),a?null:s}async function W({type:e,url:t,popped:n,keepfocus:r,noscroll:a,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:f=Ve,block:d=Ve}){const h=$;$=c;const u=await de(t,!1),l=e==="enter"?Ce(y,u,t,e):ft({url:t,type:e,delta:n?.delta,intent:u});if(!l){d(),$===c&&($=h);return}const p=A,_=I;f(),X=!0,oe&&l.navigation.type!=="enter"&&N.navigating.set(J.current=l.navigation);let m=u&&await lt(u);if(!m){if(le(t,U,v.hash))return await q(t);m=await ut(t,{id:null},await B(new Re(404,"Not Found",`Not found: ${t.pathname}`),{url:t,params:{},route:{id:null}}),404)}if(t=u?.url||t,$!==c)return l.reject(new Error("navigation aborted")),!1;if(m.type==="redirect")if(o>=20)m=await ue({status:500,error:await B(new Error("Redirect loop"),{url:t,params:{},route:{id:null}}),url:t,route:{id:null}});else return await st(new URL(m.location,t).href,{},o+1,c),!1;else m.props.page.status>=400&&await N.updated.check()&&(await et(),await q(t));if(nn(),Ie(p),at(_),m.props.page.url.pathname!==t.pathname&&(t.pathname=m.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,w={[V]:A+=g,[Y]:I+=g,[Ye]:i};(s?history.replaceState:history.pushState).call(history,w,"",t),s||en(A,I)}if(O=null,m.props.page.state=i,oe){y=m.state,m.props.page&&(m.props.page.url=t);const g=(await Promise.all(Array.from(tn,w=>w(l.navigation)))).filter(w=>typeof w=="function");if(g.length>0){let w=function(){g.forEach(b=>{H.delete(b)})};g.push(w),g.forEach(b=>{H.add(b)})}rt.$set(m.props),Jt(m.props.page),nt=!0}else it(m,be,!1);const{activeElement:R}=document;await pt();const E=n?n.scroll:a?Se():null;if(Be){const g=t.hash&&document.getElementById(decodeURIComponent(v.hash?t.hash.split("#")[2]??"":t.hash.slice(1)));E?scrollTo(E.x,E.y):g?g.scrollIntoView():scrollTo(0,0)}const L=document.activeElement!==R&&document.activeElement!==document.body;!r&&!L&&hn(),Be=!0,m.props.page&&Object.assign(S,m.props.page),X=!1,e==="popstate"&&ot(I),l.fulfil(void 0),H.forEach(g=>g(l.navigation)),N.navigating.set(J.current=null)}async function ut(e,t,n,r){return e.origin===ce&&e.pathname===location.pathname&&!Le?await ue({status:r,error:n,url:e,route:t}):await q(e)}function ln(){let e,t,n;C.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(e),e=setTimeout(()=>{s(c,j.hover)},20)});function r(o){o.defaultPrevented||s(o.composedPath()[0],j.tap)}C.addEventListener("mousedown",r),C.addEventListener("touchstart",r,{passive:!0});const a=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(we(new URL(c.target.href)),a.unobserve(c.target))},{threshold:0});async function s(o,c){const f=Xe(o,C),d=f===t&&c>=n;if(!f||d)return;const{url:h,external:u,download:l}=ye(f,U,v.hash);if(u||l)return;const p=ee(f),_=h&&ie(y.url)===ie(h);if(!(p.reload||_))if(c<=p.preload_data){t=f,n=j.tap;const m=await de(h,!1);if(!m)return;rn(m)}else c<=p.preload_code&&(t=f,n=c,we(h))}function i(){a.disconnect();for(const o of C.querySelectorAll("a")){const{url:c,external:f,download:d}=ye(o,U,v.hash);if(f||d)continue;const h=ee(o);h.reload||(h.preload_code===j.viewport&&a.observe(o),h.preload_code===j.eager&&we(c))}}H.add(i),i()}function B(e,t){if(e instanceof fe)return e.body;const n=te(e),r=Wt(e);return v.hooks.handleError({error:e,event:t,status:n,message:r})??{message:r}}function fn(e){if(typeof e=="function")re.push(e);else{const{href:t}=new URL(e,location.href);re.push(n=>n.href===t)}}function un(){history.scrollRestoration="manual",addEventListener("beforeunload",t=>{let n=!1;if(qe(),!X){const r=Ce(y,void 0,null,"leave"),a={...r.navigation,cancel:()=>{n=!0,r.reject(new Error("navigation cancelled"))}};tt.forEach(s=>s(a))}n?(t.preventDefault(),t.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&qe()}),navigator.connection?.saveData||ln(),C.addEventListener("click",async t=>{if(t.button||t.which!==1||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.defaultPrevented)return;const n=Xe(t.composedPath()[0],C);if(!n)return;const{url:r,external:a,target:s,download:i}=ye(n,U,v.hash);if(!r)return;if(s==="_parent"||s==="_top"){if(window.parent!==window)return}else if(s&&s!=="_self")return;const o=ee(n);if(!(n instanceof SVGAElement)&&r.protocol!==location.protocol&&!(r.protocol==="https:"||r.protocol==="http:")||i)return;const[f,d]=(v.hash?r.hash.replace(/^#/,""):r.href).split("#"),h=f===ge(location);if(a||o.reload&&(!h||!d)){ft({url:r,type:"link"})?X=!0:t.preventDefault();return}if(d!==void 0&&h){const[,u]=y.url.href.split("#");if(u===d){if(t.preventDefault(),d===""||d==="top"&&n.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const l=n.ownerDocument.getElementById(decodeURIComponent(d));l&&(l.scrollIntoView(),l.focus())}return}if(M=!0,Ie(A),e(r),!o.replace_state)return;M=!1}t.preventDefault(),await new Promise(u=>{requestAnimationFrame(()=>{setTimeout(u,0)}),setTimeout(u,100)}),await W({type:"link",url:r,keepfocus:o.keepfocus,noscroll:o.noscroll,replace_state:o.replace_state??r.href===location.href})}),C.addEventListener("submit",t=>{if(t.defaultPrevented)return;const n=HTMLFormElement.prototype.cloneNode.call(t.target),r=t.submitter;if((r?.formTarget||n.target)==="_blank"||(r?.formMethod||n.method)!=="get")return;const i=new URL(r?.hasAttribute("formaction")&&r?.formAction||n.action);if(le(i,U,!1))return;const o=t.target,c=ee(o);if(c.reload)return;t.preventDefault(),t.stopPropagation();const f=new FormData(o),d=r?.getAttribute("name");d&&f.append(d,r?.getAttribute("value")??""),i.search=new URLSearchParams(f).toString(),W({type:"form",url:i,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??i.href===location.href})}),addEventListener("popstate",async t=>{if(t.state?.[V]){const n=t.state[V];if($={},n===A)return;const r=D[n],a=t.state[Ye]??{},s=new URL(t.state[Pt]??location.href),i=t.state[Y],o=y.url?ge(location)===ge(y.url):!1;if(i===I&&(nt||o)){a!==S.state&&(S.state=a),e(s),D[A]=Se(),r&&scrollTo(r.x,r.y),A=n;return}const f=n-A;await W({type:"popstate",url:s,popped:{state:a,scroll:r,delta:f},accept:()=>{A=n,I=i},block:()=>{history.go(-f)},nav_token:$})}else if(!M){const n=new URL(location.href);e(n),v.hash&&location.reload()}}),addEventListener("hashchange",()=>{M&&(M=!1,history.replaceState({...history.state,[V]:++A,[Y]:I},"",location.href))});for(const t of document.querySelectorAll("link"))Qt.has(t.rel)&&(t.href=t.href);addEventListener("pageshow",t=>{t.persisted&&N.navigating.set(J.current=null)});function e(t){y.url=S.url=t,N.page.set(Oe(S)),N.page.notify()}}async function dn(e,{status:t=200,error:n,node_ids:r,params:a,route:s,server_route:i,data:o,form:c}){Le=!0;const f=new URL(location.href);let d;({params:a={},route:s={id:null}}=await de(f,!1)||{}),d=Ue.find(({id:l})=>l===s.id);let h,u=!0;try{const l=r.map(async(_,m)=>{const R=o[m];return R?.uses&&(R.uses=ht(R.uses)),xe({loader:v.nodes[_],url:f,params:a,route:s,parent:async()=>{const E={};for(let L=0;L<m;L+=1)Object.assign(E,(await l[L]).data);return E},server_data_node:Pe(R)})}),p=await Promise.all(l);if(d){const _=d.layouts;for(let m=0;m<_.length;m++)_[m]||p.splice(m,0,void 0)}h=se({url:f,params:a,branch:p,status:t,error:n,form:c,route:d??null})}catch(l){if(l instanceof Ee){await q(new URL(l.location,location.href));return}h=await ue({status:te(l),error:await B(l,{url:f,params:a,route:s}),url:f,route:s}),e.textContent="",u=!1}h.props.page&&(h.props.page.state={}),it(h,e,u)}async function dt(e,t){const n=new URL(e);n.pathname=Zt(e.pathname),e.pathname.endsWith("/")&&n.searchParams.append(Kt,"1"),n.searchParams.append(Ht,t.map(s=>s?"1":"0").join(""));const r=window.fetch,a=await r(n.href,{});if(!a.ok){let s;throw a.headers.get("content-type")?.includes("application/json")?s=await a.json():a.status===404?s="Not Found":a.status===500&&(s="Internal Error"),new fe(a.status,s)}return new Promise(async s=>{const i=new Map,o=a.body.getReader(),c=new TextDecoder;function f(h){return qt(h,{...v.decoders,Promise:u=>new Promise((l,p)=>{i.set(u,{fulfil:l,reject:p})})})}let d="";for(;;){const{done:h,value:u}=await o.read();if(h&&!d)break;for(d+=!u&&d?`
`:c.decode(u,{stream:!0});;){const l=d.indexOf(`
`);if(l===-1)break;const p=JSON.parse(d.slice(0,l));if(d=d.slice(l+1),p.type==="redirect")return s(p);if(p.type==="data")p.nodes?.forEach(_=>{_?.type==="data"&&(_.uses=ht(_.uses),_.data=f(_.data))}),s(p);else if(p.type==="chunk"){const{id:_,data:m,error:R}=p,E=i.get(_);i.delete(_),R?E.reject(f(R)):E.fulfil(f(m))}}}})}function ht(e){return{dependencies:new Set(e?.dependencies??[]),params:new Set(e?.params??[]),parent:!!e?.parent,route:!!e?.route,url:!!e?.url,search_params:new Set(e?.search_params??[])}}function hn(){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const t=document.body,n=t.getAttribute("tabindex");t.tabIndex=-1,t.focus({preventScroll:!0,focusVisible:!1}),n!==null?t.setAttribute("tabindex",n):t.removeAttribute("tabindex");const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let s=0;s<r.rangeCount;s+=1)a.push(r.getRangeAt(s));setTimeout(()=>{if(r.rangeCount===a.length){for(let s=0;s<r.rangeCount;s+=1){const i=a[s],o=r.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}r.removeAllRanges()}})}}}function Ce(e,t,n,r){let a,s;const i=new Promise((c,f)=>{a=c,s=f});return i.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:e.route?.id??null},url:e.url},to:n&&{params:t?.params??null,route:{id:t?.route?.id??null},url:n},willUnload:!t,type:r,complete:i},fulfil:a,reject:s}}function Oe(e){return{data:e.data,error:e.error,form:e.form,params:e.params,route:e.route,state:e.state,status:e.status,url:e.url}}function pn(e){const t=new URL(e);return t.hash=decodeURIComponent(e.hash),t}export{bn as a,_n as l,S as p,N as s};
