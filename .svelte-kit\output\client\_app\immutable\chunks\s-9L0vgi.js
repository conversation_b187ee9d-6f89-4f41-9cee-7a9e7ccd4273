import{i as G,m as N,y as H,E as Z,z as $,H as w,A as z,B as V,C as j,D as L,F as h,j as p,G as U,U as J,o as K,k as Y,I as M,g as d,J as Q,K as W,L as C,M as X,N as k,O as x,P as ee,S as re,Q as ae,R as se,T as ne,f as q,V as ue,b as y,W as te,X as ie,Y as fe,Z as le,_ as _e}from"./BxdUnLNP.js";import{s as ce,g as ve}from"./8hOwRzcT.js";function Ie(e,n,[s,r]=[0,0]){N&&s===0&&H();var t=e,u=null,i=null,b=J,m=s>0?Z:0,l=!1;const P=(f,_=!0)=>{l=!0,o(_,f)},o=(f,_)=>{if(b===(b=f))return;let I=!1;if(N&&r!==-1){if(s===0){const c=$(t);c===w?r=0:c===z?r=1/0:(r=parseInt(c.substring(1)),r!==r&&(r=b?1/0:-1))}const g=r>s;!!b===g&&(t=V(),j(t),L(!1),I=!0,r=-1)}b?(u?h(u):_&&(u=p(()=>_(t))),i&&U(i,()=>{i=null})):(i?h(i):_&&(i=p(()=>_(t,[s+1,r]))),u&&U(u,()=>{u=null})),I&&L(!0)};G(()=>{l=!1,n(P),l||o(null,null)},m),N&&(t=K)}let E=!1,O=Symbol();function Se(e,n,s){const r=s[n]??={store:null,source:M(void 0),unsubscribe:Y};if(r.store!==e&&!(O in s))if(r.unsubscribe(),r.store=e??null,e==null)r.source.v=void 0,r.unsubscribe=Y;else{var t=!0;r.unsubscribe=ce(e,u=>{t?r.source.v=u:C(r.source,u)}),t=!1}return e&&O in s?ve(e):d(r.source)}function me(){const e={};function n(){Q(()=>{for(var s in e)e[s].unsubscribe();W(e,O,{enumerable:!1,value:!0})})}return[e,n]}function oe(e){var n=E;try{return E=!1,[e(),E]}finally{E=n}}function B(e){return e.ctx?.d??!1}function ge(e,n,s,r){var t=(s&_e)!==0,u=!se||(s&ne)!==0,i=(s&ue)!==0,b=(s&le)!==0,m=!1,l;i?[l,m]=oe(()=>e[n]):l=e[n];var P=re in e||ae in e,o=i&&(X(e,n)?.set??(P&&n in e&&(a=>e[n]=a)))||void 0,f=r,_=!0,I=!1,g=()=>(I=!0,_&&(_=!1,b?f=y(r):f=r),f);l===void 0&&r!==void 0&&(o&&u&&k(),l=g(),o&&o(l));var c;if(u)c=()=>{var a=e[n];return a===void 0?g():(_=!0,I=!1,a)};else{var D=(t?q:ee)(()=>e[n]);D.f|=x,c=()=>{var a=d(D);return a!==void 0&&(f=void 0),a===void 0?f:a}}if((s&te)===0)return c;if(o){var F=e.$$legacy;return function(a,S){return arguments.length>0?((!u||!S||F||m)&&o(S?c():a),a):c()}}var T=!1,A=M(l),v=q(()=>{var a=c(),S=d(A);return T?(T=!1,S):A.v=a});return i&&d(v),t||(v.equals=ie),function(a,S){if(arguments.length>0){const R=S?d(v):u&&i?fe(a):a;if(!v.equals(R)){if(T=!0,C(A,R),I&&f!==void 0&&(f=R),B(v))return a;y(()=>d(v))}return a}return B(v)?v.v:d(v)}}export{me as a,Ie as i,ge as p,Se as s};
