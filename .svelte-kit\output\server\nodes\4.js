

export const index = 4;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/sidepanel/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/4.Cgablr7G.js","_app/immutable/chunks/DzLgamHK.js","_app/immutable/chunks/BxdUnLNP.js","_app/immutable/chunks/CsYk2EZE.js","_app/immutable/chunks/R9Shl-co.js","_app/immutable/chunks/CDb_H_Tf.js","_app/immutable/chunks/8hOwRzcT.js","_app/immutable/chunks/s-9L0vgi.js","_app/immutable/chunks/D7JjSAwG.js"];
export const stylesheets = ["_app/immutable/assets/App.BRvRBMPF.css","_app/immutable/assets/4.CU7t7kQJ.css"];
export const fonts = [];
