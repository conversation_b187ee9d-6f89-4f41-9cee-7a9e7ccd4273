

export const index = 4;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/sidepanel/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/4.D-Tal-vB.js","_app/immutable/chunks/DpgcOpH8.js","_app/immutable/chunks/DqHgutWf.js","_app/immutable/chunks/C1sE6Cnk.js","_app/immutable/chunks/CBEuTvut.js","_app/immutable/chunks/Cw7DmGe3.js","_app/immutable/chunks/DEldJQSp.js","_app/immutable/chunks/CinGyS9S.js","_app/immutable/chunks/D-Tr_g7H.js"];
export const stylesheets = ["_app/immutable/assets/App.BRvRBMPF.css","_app/immutable/assets/4.CU7t7kQJ.css"];
export const fonts = [];
