import type { TabInfo } from '../types';

/**
 * Get the current active tab
 */
export async function getCurrentTab(): Promise<TabInfo | null> {
  try {
    if (typeof chrome !== 'undefined' && chrome.tabs) {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      return tabs[0] || null;
    }
    
    // Fallback: try to get tab info from background script
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_CURRENT_TAB' }, (response) => {
        resolve(response?.tab || null);
      });
    });
  } catch (error) {
    console.error('Failed to get current tab:', error);
    return null;
  }
}

/**
 * Extract domain from URL
 */
export function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    console.error('Failed to extract domain from URL:', url, error);
    return null;
  }
}

/**
 * Check if we're running in a Chrome extension context
 */
export function isExtensionContext(): boolean {
  return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
}

/**
 * Format domain for display (remove www. prefix)
 */
export function formatDomain(domain: string | null): string {
  if (!domain) return 'Global';
  return domain.replace(/^www\./, '');
}

/**
 * Listen for tab changes and URL updates
 */
export function setupTabListener(callback: (tab: TabInfo | null) => void) {
  if (!isExtensionContext()) return;

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener((message) => {
    if (message.type === 'TAB_CHANGED' || message.type === 'URL_CHANGED') {
      getCurrentTab().then(callback);
    }
  });
}

/**
 * Get storage data
 */
export async function getStorageData<T>(key: string): Promise<T | null> {
  try {
    if (!isExtensionContext()) return null;
    
    const result = await chrome.storage.local.get([key]);
    return result[key] || null;
  } catch (error) {
    console.error('Failed to get storage data:', error);
    return null;
  }
}

/**
 * Set storage data
 */
export async function setStorageData<T>(key: string, data: T): Promise<boolean> {
  try {
    if (!isExtensionContext()) return false;
    
    await chrome.storage.local.set({ [key]: data });
    return true;
  } catch (error) {
    console.error('Failed to set storage data:', error);
    return false;
  }
}
