import { I as fallback, J as attr_class, F as escape_html, K as slot, M as bind_props, C as pop, z as push, N as stringify, O as attr, P as store_get, Q as unsubscribe_stores, R as ensure_array_like } from "./index.js";
import { d as derived, w as writable } from "./index2.js";
const currentDomain = writable(null);
const allItems = writable([]);
const activeTab = writable("tasks");
const modalState = writable({
  isOpen: false,
  type: null,
  item: null
});
const filterState = writable({
  showGlobal: true,
  showContextual: true,
  searchTerm: ""
});
const filteredItems = derived(
  [allItems, currentDomain, filterState, activeTab],
  ([$allItems, $currentDomain, $filterState, $activeTab]) => {
    let items = $allItems;
    if ($activeTab === "tasks") {
      items = items.filter((item) => item.type === "task" && !item.completed);
    } else if ($activeTab === "notes") {
      items = items.filter((item) => item.type === "note");
    } else if ($activeTab === "completed") {
      items = items.filter((item) => item.type === "task" && item.completed);
    }
    items = items.filter((item) => {
      const isGlobal = item.domain === null;
      const isContextual = item.domain === $currentDomain;
      return $filterState.showGlobal && isGlobal || $filterState.showContextual && isContextual;
    });
    if ($filterState.searchTerm) {
      const searchLower = $filterState.searchTerm.toLowerCase();
      items = items.filter(
        (item) => item.content.toLowerCase().includes(searchLower) || item.details && item.details.toLowerCase().includes(searchLower) || item.tags.some((tag) => tag.toLowerCase().includes(searchLower))
      );
    }
    return items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
);
const taskCounts = derived(
  [allItems, currentDomain],
  ([$allItems, $currentDomain]) => {
    const contextualTasks = $allItems.filter(
      (item) => item.type === "task" && item.domain === $currentDomain
    );
    const globalTasks = $allItems.filter(
      (item) => item.type === "task" && item.domain === null
    );
    return {
      contextual: {
        active: contextualTasks.filter((task) => !task.completed).length,
        completed: contextualTasks.filter((task) => task.completed).length
      },
      global: {
        active: globalTasks.filter((task) => !task.completed).length,
        completed: globalTasks.filter((task) => task.completed).length
      }
    };
  }
);
function formatDomain(domain) {
  if (!domain) return "Global";
  return domain.replace(/^www\./, "");
}
function Modal($$payload, $$props) {
  push();
  let isOpen = fallback($$props["isOpen"], false);
  let title = fallback($$props["title"], "");
  let size = fallback($$props["size"], "medium");
  if (isOpen) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="modal-backdrop svelte-1ihqi4b" role="dialog" aria-modal="true" aria-labelledby="modal-title"><div${attr_class(`modal-content ${stringify(size)}`, "svelte-1ihqi4b")}><div class="modal-header svelte-1ihqi4b"><h2 id="modal-title" class="svelte-1ihqi4b">${escape_html(title)}</h2> <button class="close-button svelte-1ihqi4b" aria-label="Close modal">×</button></div> <div class="modal-body svelte-1ihqi4b"><!---->`;
    slot($$payload, $$props, "default", {});
    $$payload.out += `<!----></div></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { isOpen, title, size });
  pop();
}
function ItemForm($$payload, $$props) {
  push();
  var $$store_subs;
  let isEditing;
  let item = fallback($$props["item"], null);
  let type = fallback($$props["type"], "note");
  let content = item?.content || "";
  let details = item?.details || "";
  let tags = item?.tags.join(", ") || "";
  let isGlobal = item?.domain === null || false;
  let dueDate = item?.type === "task" && item.dueDate ? item.dueDate.split("T")[0] : "";
  isEditing = item !== null;
  $$payload.out += `<form class="item-form svelte-s3dxba"><div class="form-group svelte-s3dxba"><label for="content" class="svelte-s3dxba">${escape_html(type === "task" ? "Task" : "Note")} Content *</label> <input id="content" type="text"${attr("value", content)}${attr("placeholder", type === "task" ? "What needs to be done?" : "What do you want to remember?")} required autofocus class="svelte-s3dxba"/></div> <div class="form-group svelte-s3dxba"><label for="details" class="svelte-s3dxba">Details</label> <textarea id="details" placeholder="Additional details..." rows="3" class="svelte-s3dxba">`;
  const $$body = escape_html(details);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> `;
  if (type === "task") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="form-group svelte-s3dxba"><label for="dueDate" class="svelte-s3dxba">Due Date</label> <input id="dueDate" type="date"${attr("value", dueDate)} class="svelte-s3dxba"/></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="form-group svelte-s3dxba"><label for="tags" class="svelte-s3dxba">Tags</label> <input id="tags" type="text"${attr("value", tags)} placeholder="work, urgent, research (comma-separated)" class="svelte-s3dxba"/></div> <div class="form-group svelte-s3dxba"><label class="checkbox-label svelte-s3dxba"><input type="checkbox"${attr("checked", isGlobal, true)} class="svelte-s3dxba"/> <span class="checkmark"></span> Global (not specific to current website)</label> `;
  if (!isGlobal && store_get($$store_subs ??= {}, "$currentDomain", currentDomain)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="context-info svelte-s3dxba">This will be saved for: <strong>${escape_html(store_get($$store_subs ??= {}, "$currentDomain", currentDomain))}</strong></p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="form-actions svelte-s3dxba"><button type="button" class="btn btn-secondary svelte-s3dxba">Cancel</button> <button type="submit" class="btn btn-primary svelte-s3dxba"${attr("disabled", !content.trim(), true)}>${escape_html(isEditing ? "Update" : "Create")} ${escape_html(type === "task" ? "Task" : "Note")}</button></div></form>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { item, type });
  pop();
}
function ItemCard($$payload, $$props) {
  push();
  let item = $$props["item"];
  function formatDate(dateString) {
    const date = new Date(dateString);
    const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      ...date.getFullYear() !== currentYear && { year: "numeric" }
    });
  }
  function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit" });
  }
  function isDueSoon(dueDate) {
    const due = new Date(dueDate);
    const now = /* @__PURE__ */ new Date();
    const diffDays = Math.ceil((due.getTime() - now.getTime()) / (1e3 * 60 * 60 * 24));
    return diffDays <= 3 && diffDays >= 0;
  }
  function isOverdue(dueDate) {
    const due = new Date(dueDate);
    const now = /* @__PURE__ */ new Date();
    return due < now;
  }
  $$payload.out += `<div${attr_class(`item-card ${stringify(item.type)}`, "svelte-183j7as", {
    "completed": item.type === "task" && item.completed
  })}><div class="item-header svelte-183j7as">`;
  if (item.type === "task") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button${attr_class("task-checkbox svelte-183j7as", void 0, { "checked": item.completed })}${attr("aria-label", item.completed ? "Mark as incomplete" : "Mark as complete")}>`;
    if (item.completed) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"></path></svg>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></button>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="note-icon svelte-183j7as"><svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"></path></svg></div>`;
  }
  $$payload.out += `<!--]--> <div class="item-content svelte-183j7as"><h3 class="item-title svelte-183j7as">${escape_html(item.content)}</h3> `;
  if (item.details) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="item-details svelte-183j7as">${escape_html(item.details)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="item-actions svelte-183j7as"><button class="action-btn edit-btn svelte-183j7as"${attr("aria-label", `Edit ${stringify(item.type)}`)}><svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor"><path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708L10.5 8.207l-3-3L12.146.146zM11.207 9l-3-3L2.5 11.707V14.5a.5.5 0 0 0 .5.5h2.793L11.207 9z"></path></svg></button> <button class="action-btn delete-btn svelte-183j7as"${attr("aria-label", `Delete ${stringify(item.type)}`)}><svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"></path><path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"></path></svg></button></div></div> <div class="item-meta svelte-183j7as"><div class="meta-left svelte-183j7as"><span class="domain-tag svelte-183j7as">${escape_html(formatDomain(item.domain))}</span> `;
  if (item.type === "task" && item.dueDate) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span${attr_class("due-date svelte-183j7as", void 0, {
      "due-soon": isDueSoon(item.dueDate),
      "overdue": isOverdue(item.dueDate)
    })}>Due: ${escape_html(formatDate(item.dueDate))}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (item.tags.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(item.tags);
    $$payload.out += `<div class="tags svelte-183j7as"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let tag = each_array[$$index];
      $$payload.out += `<span class="tag svelte-183j7as">${escape_html(tag)}</span>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div class="meta-right"><span class="created-date svelte-183j7as"${attr("title", new Date(item.createdAt).toLocaleString())}>${escape_html(formatDate(item.createdAt))} at ${escape_html(formatTime(item.createdAt))}</span></div></div></div>`;
  bind_props($$props, { item });
  pop();
}
function App($$payload, $$props) {
  push();
  var $$store_subs;
  let searchInput = "";
  filterState.update((state) => ({ ...state, searchTerm: searchInput }));
  $$payload.out += `<div class="app svelte-1u8h1ny"><header class="app-header svelte-1u8h1ny"><h1 class="svelte-1u8h1ny">Website Companion</h1> `;
  if (store_get($$store_subs ??= {}, "$currentDomain", currentDomain)) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="current-site svelte-1u8h1ny">Current site: <strong>${escape_html(formatDomain(store_get($$store_subs ??= {}, "$currentDomain", currentDomain)))}</strong></p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></header> <div class="search-section svelte-1u8h1ny"><input type="text" placeholder="Search notes and tasks..."${attr("value", searchInput)} class="search-input svelte-1u8h1ny"/></div> <div class="filters svelte-1u8h1ny"><button${attr_class("filter-btn svelte-1u8h1ny", void 0, {
    "active": store_get($$store_subs ??= {}, "$filterState", filterState).showContextual
  })}>Current Site `;
  if (store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.active > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="count svelte-1u8h1ny">${escape_html(store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.active)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></button> <button${attr_class("filter-btn svelte-1u8h1ny", void 0, {
    "active": store_get($$store_subs ??= {}, "$filterState", filterState).showGlobal
  })}>Global `;
  if (store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.active > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="count svelte-1u8h1ny">${escape_html(store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.active)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></button></div> <nav class="tabs svelte-1u8h1ny"><button${attr_class("tab svelte-1u8h1ny", void 0, {
    "active": store_get($$store_subs ??= {}, "$activeTab", activeTab) === "tasks"
  })}>Active Tasks `;
  if (store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.active + store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.active > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="count svelte-1u8h1ny">${escape_html(store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.active + store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.active)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></button> <button${attr_class("tab svelte-1u8h1ny", void 0, {
    "active": store_get($$store_subs ??= {}, "$activeTab", activeTab) === "notes"
  })}>Notes</button> <button${attr_class("tab svelte-1u8h1ny", void 0, {
    "active": store_get($$store_subs ??= {}, "$activeTab", activeTab) === "completed"
  })}>Completed `;
  if (store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.completed + store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.completed > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="count svelte-1u8h1ny">${escape_html(store_get($$store_subs ??= {}, "$taskCounts", taskCounts).contextual.completed + store_get($$store_subs ??= {}, "$taskCounts", taskCounts).global.completed)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></button></nav> <div class="action-buttons svelte-1u8h1ny">`;
  if (store_get($$store_subs ??= {}, "$activeTab", activeTab) === "tasks" || store_get($$store_subs ??= {}, "$activeTab", activeTab) === "completed") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button class="add-btn svelte-1u8h1ny">+ Add Task</button>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<button class="add-btn svelte-1u8h1ny">+ Add Note</button>`;
  }
  $$payload.out += `<!--]--></div> <main class="content svelte-1u8h1ny">`;
  if (store_get($$store_subs ??= {}, "$filteredItems", filteredItems).length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="empty-state svelte-1u8h1ny">`;
    if (store_get($$store_subs ??= {}, "$activeTab", activeTab) === "tasks") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="svelte-1u8h1ny">No active tasks found.</p> <button class="add-btn svelte-1u8h1ny">Create your first task</button>`;
    } else if (store_get($$store_subs ??= {}, "$activeTab", activeTab) === "notes") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<p class="svelte-1u8h1ny">No notes found.</p> <button class="add-btn svelte-1u8h1ny">Create your first note</button>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="svelte-1u8h1ny">No completed tasks found.</p>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array = ensure_array_like(store_get($$store_subs ??= {}, "$filteredItems", filteredItems));
    $$payload.out += `<div class="items-list svelte-1u8h1ny"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let item = each_array[$$index];
      ItemCard($$payload, { item });
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]--></main></div> `;
  Modal($$payload, {
    isOpen: store_get($$store_subs ??= {}, "$modalState", modalState).isOpen && (store_get($$store_subs ??= {}, "$modalState", modalState).type === "add-note" || store_get($$store_subs ??= {}, "$modalState", modalState).type === "add-task" || store_get($$store_subs ??= {}, "$modalState", modalState).type === "edit-note" || store_get($$store_subs ??= {}, "$modalState", modalState).type === "edit-task"),
    title: store_get($$store_subs ??= {}, "$modalState", modalState).type?.includes("add") ? `Add ${store_get($$store_subs ??= {}, "$modalState", modalState).type?.includes("task") ? "Task" : "Note"}` : `Edit ${store_get($$store_subs ??= {}, "$modalState", modalState).type?.includes("task") ? "Task" : "Note"}`,
    children: ($$payload2) => {
      ItemForm($$payload2, {
        item: store_get($$store_subs ??= {}, "$modalState", modalState).item,
        type: store_get($$store_subs ??= {}, "$modalState", modalState).type?.includes("task") ? "task" : "note"
      });
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Modal($$payload, {
    isOpen: store_get($$store_subs ??= {}, "$modalState", modalState).isOpen && store_get($$store_subs ??= {}, "$modalState", modalState).type === "delete-confirm",
    title: "Confirm Delete",
    size: "small",
    children: ($$payload2) => {
      $$payload2.out += `<div class="delete-confirm svelte-1u8h1ny"><p class="svelte-1u8h1ny">Are you sure you want to delete this ${escape_html(store_get($$store_subs ??= {}, "$modalState", modalState).item?.type)}?</p> <p class="item-preview svelte-1u8h1ny">"${escape_html(store_get($$store_subs ??= {}, "$modalState", modalState).item?.content)}"</p> <div class="confirm-actions svelte-1u8h1ny"><button class="btn btn-secondary svelte-1u8h1ny">Cancel</button> <button class="btn btn-danger svelte-1u8h1ny">Delete</button></div></div>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  App as A
};
