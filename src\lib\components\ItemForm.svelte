<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { Item } from '../types';
  import { currentDomain } from '../stores';
  
  export let item: Item | null = null;
  export let type: 'note' | 'task' = 'note';
  
  const dispatch = createEventDispatcher();
  
  let content = item?.content || '';
  let details = item?.details || '';
  let tags = item?.tags.join(', ') || '';
  let isGlobal = item?.domain === null || false;
  let dueDate = (item?.type === 'task' && item.dueDate) ? item.dueDate.split('T')[0] : '';
  
  $: isEditing = item !== null;
  
  function handleSubmit() {
    if (!content.trim()) return;
    
    const tagsArray = tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    const formData = {
      type,
      content: content.trim(),
      details: details.trim() || undefined,
      domain: isGlobal ? null : $currentDomain,
      tags: tagsArray,
      ...(type === 'task' && {
        completed: item?.type === 'task' ? item.completed : false,
        dueDate: dueDate || null
      })
    };
    
    if (isEditing && item) {
      dispatch('update', { id: item.id, ...formData });
    } else {
      dispatch('create', formData);
    }
  }
  
  function handleCancel() {
    dispatch('cancel');
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="item-form">
  <div class="form-group">
    <label for="content">
      {type === 'task' ? 'Task' : 'Note'} Content *
    </label>
    <input
      id="content"
      type="text"
      bind:value={content}
      placeholder={type === 'task' ? 'What needs to be done?' : 'What do you want to remember?'}
      required
      autofocus
    />
  </div>
  
  <div class="form-group">
    <label for="details">Details</label>
    <textarea
      id="details"
      bind:value={details}
      placeholder="Additional details..."
      rows="3"
    ></textarea>
  </div>
  
  {#if type === 'task'}
    <div class="form-group">
      <label for="dueDate">Due Date</label>
      <input
        id="dueDate"
        type="date"
        bind:value={dueDate}
      />
    </div>
  {/if}
  
  <div class="form-group">
    <label for="tags">Tags</label>
    <input
      id="tags"
      type="text"
      bind:value={tags}
      placeholder="work, urgent, research (comma-separated)"
    />
  </div>
  
  <div class="form-group">
    <label class="checkbox-label">
      <input
        type="checkbox"
        bind:checked={isGlobal}
      />
      <span class="checkmark"></span>
      Global (not specific to current website)
    </label>
    {#if !isGlobal && $currentDomain}
      <p class="context-info">
        This will be saved for: <strong>{$currentDomain}</strong>
      </p>
    {/if}
  </div>
  
  <div class="form-actions">
    <button type="button" class="btn btn-secondary" on:click={handleCancel}>
      Cancel
    </button>
    <button type="submit" class="btn btn-primary" disabled={!content.trim()}>
      {isEditing ? 'Update' : 'Create'} {type === 'task' ? 'Task' : 'Note'}
    </button>
  </div>
</form>

<style>
  .item-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
  }
  
  input[type="text"],
  input[type="date"],
  textarea {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
  }
  
  input[type="text"]:focus,
  input[type="date"]:focus,
  textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  textarea {
    resize: vertical;
    min-height: 60px;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: normal;
  }
  
  input[type="checkbox"] {
    margin: 0;
  }
  
  .context-info {
    font-size: 12px;
    color: #6b7280;
    margin: 4px 0 0 0;
  }
  
  .form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 8px;
  }
  
  .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }
  
  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .btn-secondary {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
  }
  
  .btn-secondary:hover:not(:disabled) {
    background: #f3f4f6;
  }
  
  .btn-primary {
    background: #3b82f6;
    color: white;
  }
  
  .btn-primary:hover:not(:disabled) {
    background: #2563eb;
  }
</style>
