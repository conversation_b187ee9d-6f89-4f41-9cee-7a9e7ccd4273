import { writable, derived, get } from 'svelte/store';
import type { Note, Task, Item, FilterState, ModalState } from '../types';

// Current context (domain of active tab)
export const currentDomain = writable<string | null>(null);
export const currentUrl = writable<string | null>(null);

// All notes and tasks
export const allItems = writable<Item[]>([]);

// UI state
export const activeTab = writable<'tasks' | 'notes' | 'completed'>('tasks');
export const modalState = writable<ModalState>({
  isOpen: false,
  type: null,
  item: null
});

// Filter state
export const filterState = writable<FilterState>({
  showGlobal: true,
  showContextual: true,
  searchTerm: ''
});

// Derived stores for filtered items
export const filteredItems = derived(
  [allItems, currentDomain, filterState, activeTab],
  ([$allItems, $currentDomain, $filterState, $activeTab]) => {
    let items = $allItems;

    // Filter by type based on active tab
    if ($activeTab === 'tasks') {
      items = items.filter(item => item.type === 'task' && !(item as Task).completed);
    } else if ($activeTab === 'notes') {
      items = items.filter(item => item.type === 'note');
    } else if ($activeTab === 'completed') {
      items = items.filter(item => item.type === 'task' && (item as Task).completed);
    }

    // Filter by domain context
    items = items.filter(item => {
      const isGlobal = item.domain === null;
      const isContextual = item.domain === $currentDomain;
      
      return ($filterState.showGlobal && isGlobal) || 
             ($filterState.showContextual && isContextual);
    });

    // Filter by search term
    if ($filterState.searchTerm) {
      const searchLower = $filterState.searchTerm.toLowerCase();
      items = items.filter(item => 
        item.content.toLowerCase().includes(searchLower) ||
        (item.details && item.details.toLowerCase().includes(searchLower)) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Sort by creation date (newest first)
    return items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
);

// Derived store for task counts
export const taskCounts = derived(
  [allItems, currentDomain],
  ([$allItems, $currentDomain]) => {
    const contextualTasks = $allItems.filter(item => 
      item.type === 'task' && item.domain === $currentDomain
    );
    const globalTasks = $allItems.filter(item => 
      item.type === 'task' && item.domain === null
    );
    
    return {
      contextual: {
        active: contextualTasks.filter(task => !(task as Task).completed).length,
        completed: contextualTasks.filter(task => (task as Task).completed).length
      },
      global: {
        active: globalTasks.filter(task => !(task as Task).completed).length,
        completed: globalTasks.filter(task => (task as Task).completed).length
      }
    };
  }
);

// Store actions
export const storeActions = {
  // Load data from Chrome storage
  async loadData() {
    try {
      const result = await chrome.storage.local.get(['items']);
      if (result.items) {
        allItems.set(result.items);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  },

  // Save data to Chrome storage
  async saveData() {
    try {
      const items = get(allItems);
      await chrome.storage.local.set({ items });
    } catch (error) {
      console.error('Failed to save data:', error);
    }
  },

  // Add new item
  async addItem(item: Omit<Item, 'id' | 'createdAt'>) {
    const newItem: Item = {
      ...item,
      id: crypto.randomUUID(),
      createdAt: new Date().toISOString()
    };
    
    allItems.update(items => [...items, newItem]);
    await storeActions.saveData();
  },

  // Update existing item
  async updateItem(id: string, updates: Partial<Item>) {
    allItems.update(items => 
      items.map(item => 
        item.id === id ? { ...item, ...updates } : item
      )
    );
    await storeActions.saveData();
  },

  // Delete item
  async deleteItem(id: string) {
    allItems.update(items => items.filter(item => item.id !== id));
    await storeActions.saveData();
  },

  // Toggle task completion
  async toggleTask(id: string) {
    allItems.update(items => 
      items.map(item => 
        item.id === id && item.type === 'task' 
          ? { ...item, completed: !item.completed }
          : item
      )
    );
    await storeActions.saveData();
  }
};
