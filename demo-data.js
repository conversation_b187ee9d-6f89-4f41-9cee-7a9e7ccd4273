// Demo data for testing the Contextual Website Companion extension
// You can run this in the browser console to populate the extension with sample data

const demoData = [
  {
    id: 'demo-1',
    type: 'task',
    content: 'Review documentation for new API',
    details: 'Check the authentication methods and rate limits',
    domain: 'github.com',
    completed: false,
    dueDate: new Date(Date.now() + ******** * 2).toISOString(), // 2 days from now
    tags: ['work', 'api', 'urgent'],
    createdAt: new Date(Date.now() - ********).toISOString() // 1 day ago
  },
  {
    id: 'demo-2',
    type: 'note',
    content: 'Interesting article about Chrome extensions',
    details: 'This article explains the new Manifest V3 features and migration strategies',
    domain: 'developer.chrome.com',
    tags: ['development', 'chrome', 'reference'],
    createdAt: new Date(Date.now() - 3600000 * 3).toISOString() // 3 hours ago
  },
  {
    id: 'demo-3',
    type: 'task',
    content: 'Buy groceries',
    details: 'Milk, bread, eggs, and vegetables for the week',
    domain: null, // Global task
    completed: false,
    dueDate: new Date(Date.now() + ********).toISOString(), // Tomorrow
    tags: ['personal', 'shopping'],
    createdAt: new Date(Date.now() - ******** * 2).toISOString() // 2 days ago
  },
  {
    id: 'demo-4',
    type: 'task',
    content: 'Complete project proposal',
    details: 'Include budget estimates and timeline',
    domain: 'docs.google.com',
    completed: true,
    dueDate: new Date(Date.now() - ********).toISOString(), // Yesterday
    tags: ['work', 'proposal', 'completed'],
    createdAt: new Date(Date.now() - ******** * 5).toISOString() // 5 days ago
  },
  {
    id: 'demo-5',
    type: 'note',
    content: 'Recipe for chocolate chip cookies',
    details: '2 cups flour, 1 cup butter, 1/2 cup sugar, chocolate chips',
    domain: null, // Global note
    tags: ['cooking', 'recipe', 'dessert'],
    createdAt: new Date(Date.now() - ******** * 3).toISOString() // 3 days ago
  },
  {
    id: 'demo-6',
    type: 'task',
    content: 'Research SvelteKit best practices',
    details: 'Look into routing, state management, and deployment options',
    domain: 'svelte.dev',
    completed: false,
    dueDate: new Date(Date.now() + ******** * 7).toISOString(), // 1 week from now
    tags: ['learning', 'svelte', 'development'],
    createdAt: new Date().toISOString() // Now
  }
];

// Function to load demo data into the extension
async function loadDemoData() {
  try {
    await chrome.storage.local.set({ items: demoData });
    console.log('Demo data loaded successfully!');
    console.log('Reload the extension to see the sample notes and tasks.');
  } catch (error) {
    console.error('Failed to load demo data:', error);
  }
}

// Instructions
console.log('Contextual Website Companion - Demo Data');
console.log('========================================');
console.log('To load sample data into the extension:');
console.log('1. Open the extension (popup or side panel)');
console.log('2. Open browser developer tools (F12)');
console.log('3. Go to the Console tab');
console.log('4. Run: loadDemoData()');
console.log('5. Refresh the extension interface');
console.log('');
console.log('The demo data includes:');
console.log('- Tasks for different websites (GitHub, Google Docs, Svelte)');
console.log('- Global tasks and notes');
console.log('- Completed and pending items');
console.log('- Items with due dates and tags');

// Make the function available globally
window.loadDemoData = loadDemoData;
