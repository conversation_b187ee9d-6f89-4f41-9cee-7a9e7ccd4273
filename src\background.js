// Background script for Contextual Website Companion

// Install event - open side panel on first install
chrome.runtime.onInstalled.addListener(() => {
  console.log('Contextual Website Companion installed');
});

// Handle extension icon click - open side panel
chrome.action.onClicked.addListener(async (tab) => {
  try {
    await chrome.sidePanel.open({ tabId: tab.id });
  } catch (error) {
    console.error('Failed to open side panel:', error);
  }
});

// Listen for tab updates to refresh context in side panel
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    // Send message to side panel about tab change
    chrome.runtime.sendMessage({
      type: 'TAB_CHANGED',
      tabId: activeInfo.tabId
    });
  } catch (error) {
    // Side panel might not be open, which is fine
    console.log('Side panel not available for tab change notification');
  }
});

chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.url) {
    try {
      // Send message to side panel about URL change
      chrome.runtime.sendMessage({
        type: 'URL_CHANGED',
        tabId: tabId,
        url: changeInfo.url
      });
    } catch (error) {
      // Side panel might not be open, which is fine
      console.log('Side panel not available for URL change notification');
    }
  }
});

// Handle messages from content scripts or side panel
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'GET_CURRENT_TAB') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      sendResponse({ tab: tabs[0] });
    });
    return true; // Keep message channel open for async response
  }
});
