

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.Ce4uv_st.js","_app/immutable/chunks/DzLgamHK.js","_app/immutable/chunks/BxdUnLNP.js","_app/immutable/chunks/CsYk2EZE.js","_app/immutable/chunks/R9Shl-co.js","_app/immutable/chunks/D7JjSAwG.js","_app/immutable/chunks/DpCXCK4X.js","_app/immutable/chunks/8hOwRzcT.js"];
export const stylesheets = [];
export const fonts = [];
