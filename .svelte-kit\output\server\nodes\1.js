

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.BAgmuYGw.js","_app/immutable/chunks/DpgcOpH8.js","_app/immutable/chunks/DqHgutWf.js","_app/immutable/chunks/C1sE6Cnk.js","_app/immutable/chunks/CBEuTvut.js","_app/immutable/chunks/D-Tr_g7H.js","_app/immutable/chunks/BkpnSdVU.js","_app/immutable/chunks/DEldJQSp.js"];
export const stylesheets = [];
export const fonts = [];
