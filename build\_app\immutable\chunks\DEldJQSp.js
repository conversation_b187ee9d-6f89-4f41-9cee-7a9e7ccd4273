import{k as b,b as y,a0 as k,r as E,a as q,c as p,a1 as z,R as A}from"./DqHgutWf.js";function h(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function v(e,n,t){if(e==null)return n(void 0),t&&t(void 0),b;const s=y(()=>e.subscribe(n,t));return s.unsubscribe?()=>s.unsubscribe():s}const f=[];function $(e,n){return{subscribe:B(e,n).subscribe}}function B(e,n=b){let t=null;const s=new Set;function u(c){if(k(e,c)&&(e=c,t)){const i=!f.length;for(const r of s)r[1](),f.push(r,e);if(i){for(let r=0;r<f.length;r+=2)f[r][0](f[r+1]);f.length=0}}}function a(c){u(c(e))}function o(c,i=b){const r=[c,i];return s.add(r),s.size===1&&(t=n(u,a)||b),c(e),()=>{s.delete(r),s.size===0&&t&&(t(),t=null)}}return{set:u,update:a,subscribe:o}}function P(e,n,t){const s=!Array.isArray(e),u=s?[e]:e;if(!u.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=n.length<2;return $(t,(o,c)=>{let i=!1;const r=[];let _=0,d=b;const g=()=>{if(_)return;d();const l=n(s?r[0]:r,o,c);a?o(l):d=typeof l=="function"?l:b},w=u.map((l,m)=>v(l,x=>{r[m]=x,_&=~(1<<m),i&&g()},()=>{_|=1<<m}));return i=!0,g(),function(){E(w),d(),i=!1}})}function R(e){let n;return v(e,t=>n=t)(),n}function S(e){p===null&&h(),A&&p.l!==null?D(p).m.push(e):q(()=>{const n=y(e);if(typeof n=="function")return n})}function C(e,n,{bubbles:t=!1,cancelable:s=!1}={}){return new CustomEvent(e,{detail:n,bubbles:t,cancelable:s})}function j(){const e=p;return e===null&&h(),(n,t,s)=>{const u=e.s.$$events?.[n];if(u){const a=z(u)?u.slice():[u],o=C(n,t,s);for(const c of a)c.call(e.x,o);return!o.defaultPrevented}return!0}}function D(e){var n=e.l;return n.u??={a:[],b:[],m:[]}}export{j as c,P as d,R as g,S as o,v as s,B as w};
