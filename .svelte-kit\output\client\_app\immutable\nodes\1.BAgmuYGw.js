import{f as u,a as h}from"../chunks/DpgcOpH8.js";import"../chunks/C1sE6Cnk.js";import{q as g,p as v,t as l,s as d,v as a,w as e,x}from"../chunks/DqHgutWf.js";import{s as o}from"../chunks/CBEuTvut.js";import{i as _}from"../chunks/D-Tr_g7H.js";import{s as $,p}from"../chunks/BkpnSdVU.js";const k={get error(){return p.error},get status(){return p.status}};$.updated.check;const m=k;var b=u("<h1> </h1> <p> </p>",1);function A(i,f){g(f,!1),_();var t=b(),r=v(t),n=a(r,!0);e(r);var s=x(r,2),c=a(s,!0);e(s),l(()=>{o(n,m.status),o(c,m.error?.message)}),h(i,t),d()}export{A as component};
