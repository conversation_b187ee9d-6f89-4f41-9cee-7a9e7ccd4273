{"name": "@sveltejs/adapter-static", "version": "3.0.8", "description": "Adapter for SvelteKit apps that prerenders your entire site as a collection of static files", "keywords": ["adapter", "deploy", "hosting", "ssg", "static site generation", "svelte", "sveltekit"], "repository": {"type": "git", "url": "https://github.com/sveltejs/kit", "directory": "packages/adapter-static"}, "license": "MIT", "homepage": "https://svelte.dev", "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js"}, "./package.json": "./package.json"}, "types": "index.d.ts", "files": ["index.js", "index.d.ts", "platforms.js"], "devDependencies": {"@playwright/test": "^1.44.1", "@sveltejs/vite-plugin-svelte": "^5.0.1", "@types/node": "^18.19.48", "sirv": "^3.0.0", "svelte": "^5.2.9", "typescript": "^5.3.3", "vite": "^6.0.1", "@sveltejs/kit": "^2.14.0"}, "peerDependencies": {"@sveltejs/kit": "^2.0.0"}, "scripts": {"lint": "prettier --check .", "check": "tsc", "format": "pnpm lint --write", "test": "pnpm -r --workspace-concurrency 1 --filter=\"./test/**\" test"}}