<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    allItems, 
    filteredItems, 
    activeTab, 
    modalState, 
    currentDomain, 
    currentUrl,
    filterState,
    taskCounts,
    storeActions 
  } from '../stores';
  import { getCurrentTab, extractDomain, setupTabListener, formatDomain } from '../utils/chrome';
  import type { Item } from '../types';
  
  import Modal from './Modal.svelte';
  import ItemForm from './ItemForm.svelte';
  import ItemCard from './ItemCard.svelte';
  
  let searchInput = '';
  
  // Update search filter when input changes
  $: filterState.update(state => ({ ...state, searchTerm: searchInput }));
  
  onMount(async () => {
    // Load data from storage
    await storeActions.loadData();
    
    // Get current tab info
    const tab = await getCurrentTab();
    if (tab?.url) {
      currentUrl.set(tab.url);
      currentDomain.set(extractDomain(tab.url));
    }
    
    // Setup listener for tab changes
    setupTabListener(async (tab) => {
      if (tab?.url) {
        currentUrl.set(tab.url);
        currentDomain.set(extractDomain(tab.url));
      }
    });
  });
  
  function openModal(type: 'add-note' | 'add-task' | 'edit-note' | 'edit-task', item: Item | null = null) {
    modalState.set({
      isOpen: true,
      type,
      item
    });
  }
  
  function closeModal() {
    modalState.set({
      isOpen: false,
      type: null,
      item: null
    });
  }
  
  async function handleCreateItem(event: CustomEvent) {
    await storeActions.addItem(event.detail);
    closeModal();
  }
  
  async function handleUpdateItem(event: CustomEvent) {
    const { id, ...updates } = event.detail;
    await storeActions.updateItem(id, updates);
    closeModal();
  }
  
  async function handleToggleTask(event: CustomEvent) {
    await storeActions.toggleTask(event.detail);
  }
  
  function handleEditItem(event: CustomEvent) {
    const item = event.detail;
    const type = item.type === 'task' ? 'edit-task' : 'edit-note';
    openModal(type, item);
  }
  
  function handleDeleteItem(event: CustomEvent) {
    modalState.set({
      isOpen: true,
      type: 'delete-confirm',
      item: event.detail
    });
  }
  
  async function confirmDelete() {
    if ($modalState.item) {
      await storeActions.deleteItem($modalState.item.id);
      closeModal();
    }
  }
  
  function toggleFilter(type: 'global' | 'contextual') {
    filterState.update(state => ({
      ...state,
      [type === 'global' ? 'showGlobal' : 'showContextual']: 
        !state[type === 'global' ? 'showGlobal' : 'showContextual']
    }));
  }
</script>

<div class="app">
  <header class="app-header">
    <h1>Website Companion</h1>
    {#if $currentDomain}
      <p class="current-site">
        Current site: <strong>{formatDomain($currentDomain)}</strong>
      </p>
    {/if}
  </header>
  
  <div class="search-section">
    <input
      type="text"
      placeholder="Search notes and tasks..."
      bind:value={searchInput}
      class="search-input"
    />
  </div>
  
  <div class="filters">
    <button 
      class="filter-btn"
      class:active={$filterState.showContextual}
      on:click={() => toggleFilter('contextual')}
    >
      Current Site
      {#if $taskCounts.contextual.active > 0}
        <span class="count">{$taskCounts.contextual.active}</span>
      {/if}
    </button>
    <button 
      class="filter-btn"
      class:active={$filterState.showGlobal}
      on:click={() => toggleFilter('global')}
    >
      Global
      {#if $taskCounts.global.active > 0}
        <span class="count">{$taskCounts.global.active}</span>
      {/if}
    </button>
  </div>
  
  <nav class="tabs">
    <button 
      class="tab"
      class:active={$activeTab === 'tasks'}
      on:click={() => activeTab.set('tasks')}
    >
      Active Tasks
      {#if $taskCounts.contextual.active + $taskCounts.global.active > 0}
        <span class="count">{$taskCounts.contextual.active + $taskCounts.global.active}</span>
      {/if}
    </button>
    <button 
      class="tab"
      class:active={$activeTab === 'notes'}
      on:click={() => activeTab.set('notes')}
    >
      Notes
    </button>
    <button 
      class="tab"
      class:active={$activeTab === 'completed'}
      on:click={() => activeTab.set('completed')}
    >
      Completed
      {#if $taskCounts.contextual.completed + $taskCounts.global.completed > 0}
        <span class="count">{$taskCounts.contextual.completed + $taskCounts.global.completed}</span>
      {/if}
    </button>
  </nav>
  
  <div class="action-buttons">
    {#if $activeTab === 'tasks' || $activeTab === 'completed'}
      <button class="add-btn" on:click={() => openModal('add-task')}>
        + Add Task
      </button>
    {:else}
      <button class="add-btn" on:click={() => openModal('add-note')}>
        + Add Note
      </button>
    {/if}
  </div>
  
  <main class="content">
    {#if $filteredItems.length === 0}
      <div class="empty-state">
        {#if $activeTab === 'tasks'}
          <p>No active tasks found.</p>
          <button class="add-btn" on:click={() => openModal('add-task')}>
            Create your first task
          </button>
        {:else if $activeTab === 'notes'}
          <p>No notes found.</p>
          <button class="add-btn" on:click={() => openModal('add-note')}>
            Create your first note
          </button>
        {:else}
          <p>No completed tasks found.</p>
        {/if}
      </div>
    {:else}
      <div class="items-list">
        {#each $filteredItems as item (item.id)}
          <ItemCard 
            {item}
            on:toggle={handleToggleTask}
            on:edit={handleEditItem}
            on:delete={handleDeleteItem}
          />
        {/each}
      </div>
    {/if}
  </main>
</div>

<!-- Modals -->
<Modal 
  isOpen={$modalState.isOpen && ($modalState.type === 'add-note' || $modalState.type === 'add-task' || $modalState.type === 'edit-note' || $modalState.type === 'edit-task')}
  title={$modalState.type?.includes('add') ? 
    `Add ${$modalState.type?.includes('task') ? 'Task' : 'Note'}` : 
    `Edit ${$modalState.type?.includes('task') ? 'Task' : 'Note'}`}
  on:close={closeModal}
>
  <ItemForm
    item={$modalState.item}
    type={$modalState.type?.includes('task') ? 'task' : 'note'}
    on:create={handleCreateItem}
    on:update={handleUpdateItem}
    on:cancel={closeModal}
  />
</Modal>

<Modal 
  isOpen={$modalState.isOpen && $modalState.type === 'delete-confirm'}
  title="Confirm Delete"
  size="small"
  on:close={closeModal}
>
  <div class="delete-confirm">
    <p>Are you sure you want to delete this {$modalState.item?.type}?</p>
    <p class="item-preview">"{$modalState.item?.content}"</p>
    <div class="confirm-actions">
      <button class="btn btn-secondary" on:click={closeModal}>Cancel</button>
      <button class="btn btn-danger" on:click={confirmDelete}>Delete</button>
    </div>
  </div>
</Modal>

<style>
  .app {
    max-width: 400px;
    margin: 0 auto;
    background: #f9fafb;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  .app-header {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .app-header h1 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
  
  .current-site {
    margin: 0;
    font-size: 12px;
    color: #6b7280;
  }
  
  .search-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .filters {
    display: flex;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .filter-btn {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
  }
  
  .filter-btn:hover {
    border-color: #3b82f6;
  }
  
  .filter-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }
  
  .tabs {
    display: flex;
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .tab {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: none;
    color: #6b7280;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }
  
  .tab:hover {
    color: #374151;
  }
  
  .tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
  }
  
  .count {
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
  }
  
  .filter-btn.active .count {
    background: rgba(255, 255, 255, 0.3);
  }
  
  .action-buttons {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .add-btn {
    width: 100%;
    padding: 10px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
  }
  
  .add-btn:hover {
    background: #2563eb;
  }
  
  .content {
    flex: 1;
    padding: 16px 20px;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
  }
  
  .empty-state p {
    margin: 0 0 16px 0;
  }
  
  .items-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .delete-confirm {
    text-align: center;
  }
  
  .delete-confirm p {
    margin: 0 0 12px 0;
    color: #374151;
  }
  
  .item-preview {
    font-style: italic;
    color: #6b7280;
    background: #f3f4f6;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 12px 0 20px 0;
  }
  
  .confirm-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
  }
  
  .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
  }
  
  .btn-secondary {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
  }
  
  .btn-secondary:hover {
    background: #f3f4f6;
  }
  
  .btn-danger {
    background: #dc2626;
    color: white;
  }
  
  .btn-danger:hover {
    background: #b91c1c;
  }
</style>
