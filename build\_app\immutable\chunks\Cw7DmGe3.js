import{c as At,a as T,f as E,d as Et}from"./DpgcOpH8.js";import"./C1sE6Cnk.js";import{d as Ze,w as me,g as $t,c as je,o as It}from"./DEldJQSp.js";import{C as Oe,m as H,a3 as Mt,a2 as Lt,y as et,i as Nt,g as A,P as K,z as St,A as Ot,B as qe,D as Ve,o as ye,ah as Vt,F as tt,j as at,G as zt,ak as st,a1 as Gt,I as re,as as Fe,at as ze,a5 as Ye,au as Ht,av as jt,aw as Ut,a8 as Rt,ax as Bt,l as qt,ay as Ft,az as Yt,ae as Pt,aA as Wt,aB as Kt,aC as Xt,aD as Jt,aE as Qt,aF as Zt,b as nt,an as rt,q as Me,p as $e,s as Le,aG as ea,v as d,w as i,x as m,t as z,aH as lt,L as ce,e as ta,aI as ot,n as aa}from"./DqHgutWf.js";import{b as sa,l as it,e as I,s as S,r as na}from"./CBEuTvut.js";import{p as he,i as N,s as ve,a as ct}from"./CinGyS9S.js";import{i as Ne}from"./D-Tr_g7H.js";function ra(e,a){return a}function la(e,a,t,n){for(var s=[],r=a.length,l=0;l<r;l++)Ut(a[l].e,s,!0);var c=r>0&&s.length===0&&t!==null;if(c){var g=t.parentNode;Rt(g),g.append(t),n.clear(),le(e,a[0].prev,a[r-1].next)}Bt(s,()=>{for(var k=0;k<r;k++){var p=a[k];c||(n.delete(p.k),le(e,p.prev,p.next)),qt(p.e,!c)}})}function vt(e,a,t,n,s,r=null){var l=e,c={flags:a,items:new Map,first:null};{var g=e;l=H?Oe(Mt(g)):g.appendChild(Lt())}H&&et();var k=null,p=!1,w=K(()=>{var v=t();return Gt(v)?v:v==null?[]:st(v)});Nt(()=>{var v=A(w),_=v.length;if(p&&_===0)return;p=_===0;let y=!1;if(H){var O=St(l)===Ot;O!==(_===0)&&(l=qe(),Oe(l),Ve(!1),y=!0)}if(H){for(var $=null,b,C=0;C<_;C++){if(ye.nodeType===8&&ye.data===Vt){l=ye,y=!0,Ve(!1);break}var G=v[C],M=n(G,C);b=dt(ye,c,$,null,G,M,C,s,a,t),c.items.set(M,b),$=b}_>0&&Oe(qe())}H||oa(v,c,l,s,a,n,t),r!==null&&(_===0?k?tt(k):k=at(()=>r(l)):k!==null&&zt(k,()=>{k=null})),y&&Ve(!0),A(w)}),H&&(l=ye)}function oa(e,a,t,n,s,r,l){var c=e.length,g=a.items,k=a.first,p=k,w,v=null,_=[],y=[],O,$,b,C;for(C=0;C<c;C+=1){if(O=e[C],$=r(O,C),b=g.get($),b===void 0){var G=p?p.e.nodes_start:t;v=dt(G,a,v,v===null?a.first:v.next,O,$,C,n,s,l),g.set($,v),_=[],y=[],p=v.next;continue}if(ia(b,O,C),(b.e.f&ze)!==0&&tt(b.e),b!==p){if(w!==void 0&&w.has(b)){if(_.length<y.length){var M=y[0],L;v=M.prev;var U=_[0],R=_[_.length-1];for(L=0;L<_.length;L+=1)Pe(_[L],M,t);for(L=0;L<y.length;L+=1)w.delete(y[L]);le(a,U.prev,R.next),le(a,v,U),le(a,R,M),p=M,v=R,C-=1,_=[],y=[]}else w.delete(b),Pe(b,p,t),le(a,b.prev,b.next),le(a,b,v===null?a.first:v.next),le(a,v,b),v=b;continue}for(_=[],y=[];p!==null&&p.k!==$;)(p.e.f&ze)===0&&(w??=new Set).add(p),y.push(p),p=p.next;if(p===null)continue;b=p}_.push(b),v=b,p=b.next}if(p!==null||w!==void 0){for(var X=w===void 0?[]:st(w);p!==null;)(p.e.f&ze)===0&&X.push(p),p=p.next;var B=X.length;if(B>0){var q=c===0?t:null;la(a,X,q,g)}}Ye.first=a.first&&a.first.e,Ye.last=v&&v.e}function ia(e,a,t,n){jt(e.v,a),e.i=t}function dt(e,a,t,n,s,r,l,c,g,k){var p=(g&Ft)!==0,w=(g&Yt)===0,v=p?w?re(s):Fe(s):s,_=(g&Ht)===0?l:Fe(l),y={i:_,v,k:r,a:null,e:null,prev:t,next:n};try{return y.e=at(()=>c(e,v,_,k),H),y.e.prev=t&&t.e,y.e.next=n&&n.e,t===null?a.first=y:(t.next=y,t.e.next=y.e),n!==null&&(n.prev=y,n.e.prev=y.e),y}finally{}}function Pe(e,a,t){for(var n=e.next?e.next.e.nodes_start:t,s=a?a.e.nodes_start:t,r=e.e.nodes_start;r!==n;){var l=Pt(r);s.before(r),r=l}}function le(e,a,t){a===null?e.first=t:(a.next=t,a.e.next=t&&t.e),t!==null&&(t.prev=a,t.e.prev=a&&a.e)}function ca(e,a,t,n,s){H&&et();var r=a.$$slots?.[t],l=!1;r===!0&&(r=a.children,l=!0),r===void 0||r(e,l?()=>n:n)}const We=[...` 	
\r\f \v\uFEFF`];function va(e,a,t){var n=e==null?"":""+e;if(a&&(n=n?n+" "+a:a),t){for(var s in t)if(t[s])n=n?n+" "+s:s;else if(n.length)for(var r=s.length,l=0;(l=n.indexOf(s,l))>=0;){var c=l+r;(l===0||We.includes(n[l-1]))&&(c===n.length||We.includes(n[c]))?n=(l===0?"":n.substring(0,l))+n.substring(c+1):l=c}}return n===""?null:n}function ee(e,a,t,n,s,r){var l=e.__className;if(H||l!==t||l===void 0){var c=va(t,n,r);(!H||c!==e.getAttribute("class"))&&(c==null?e.removeAttribute("class"):e.className=c),e.__className=t}else if(r&&s!==r)for(var g in r){var k=!!r[g];(s==null||k!==!!s[g])&&e.classList.toggle(g,k)}return r}const da=Symbol("is custom element"),ua=Symbol("is html");function ke(e){if(H){var a=!1,t=()=>{if(!a){if(a=!0,e.hasAttribute("value")){var n=e.value;de(e,"value",null),e.value=n}if(e.hasAttribute("checked")){var s=e.checked;de(e,"checked",null),e.checked=s}}};e.__on_r=t,Wt(t),sa()}}function de(e,a,t,n){var s=fa(e);H&&(s[a]=e.getAttribute(a),a==="src"||a==="srcset"||a==="href"&&e.nodeName==="LINK")||s[a]!==(s[a]=t)&&(a==="loading"&&(e[Jt]=t),t==null?e.removeAttribute(a):typeof t!="string"&&ha(e).includes(a)?e[a]=t:e.setAttribute(a,t))}function fa(e){return e.__attributes??={[da]:e.nodeName.includes("-"),[ua]:e.namespaceURI===Kt}}var Ke=new Map;function ha(e){var a=Ke.get(e.nodeName);if(a)return a;Ke.set(e.nodeName,a=[]);for(var t,n=e,s=Element.prototype;s!==n;){t=Qt(n);for(var r in t)t[r].set&&a.push(r);n=Xt(n)}return a}function we(e,a,t=a){var n=Zt();it(e,"input",s=>{var r=s?e.defaultValue:e.value;if(r=Ge(e)?He(r):r,t(r),n&&r!==(r=a())){var l=e.selectionStart,c=e.selectionEnd;e.value=r??"",c!==null&&(e.selectionStart=l,e.selectionEnd=Math.min(c,e.value.length))}}),(H&&e.defaultValue!==e.value||nt(a)==null&&e.value)&&t(Ge(e)?He(e.value):e.value),rt(()=>{var s=a();Ge(e)&&s===He(e.value)||e.type==="date"&&!s&&!e.value||s!==e.value&&(e.value=s??"")})}function pa(e,a,t=a){it(e,"change",n=>{var s=n?e.defaultChecked:e.checked;t(s)}),(H&&e.defaultChecked!==e.checked||nt(a)==null)&&t(e.checked),rt(()=>{var n=a();e.checked=!!n})}function Ge(e){var a=e.type;return a==="number"||a==="range"}function He(e){return e===""?null:+e}function ma(e){return function(...a){var t=a[0];return t.preventDefault(),e?.apply(this,a)}}const pe=me(null),Xe=me(null),oe=me([]),xe=me("tasks"),Ee=me({isOpen:!1,type:null,item:null}),Ie=me({showGlobal:!0,showContextual:!0,searchTerm:""}),_a=Ze([oe,pe,Ie,xe],([e,a,t,n])=>{let s=e;if(n==="tasks"?s=s.filter(r=>r.type==="task"&&!r.completed):n==="notes"?s=s.filter(r=>r.type==="note"):n==="completed"&&(s=s.filter(r=>r.type==="task"&&r.completed)),s=s.filter(r=>{const l=r.domain===null,c=r.domain===a;return t.showGlobal&&l||t.showContextual&&c}),t.searchTerm){const r=t.searchTerm.toLowerCase();s=s.filter(l=>l.content.toLowerCase().includes(r)||l.details&&l.details.toLowerCase().includes(r)||l.tags.some(c=>c.toLowerCase().includes(r)))}return s.sort((r,l)=>new Date(l.createdAt).getTime()-new Date(r.createdAt).getTime())}),ba=Ze([oe,pe],([e,a])=>{const t=e.filter(s=>s.type==="task"&&s.domain===a),n=e.filter(s=>s.type==="task"&&s.domain===null);return{contextual:{active:t.filter(s=>!s.completed).length,completed:t.filter(s=>s.completed).length},global:{active:n.filter(s=>!s.completed).length,completed:n.filter(s=>s.completed).length}}}),te={async loadData(){try{const e=await chrome.storage.local.get(["items"]);e.items&&oe.set(e.items)}catch(e){console.error("Failed to load data:",e)}},async saveData(){try{const e=$t(oe);await chrome.storage.local.set({items:e})}catch(e){console.error("Failed to save data:",e)}},async addItem(e){const a={...e,id:crypto.randomUUID(),createdAt:new Date().toISOString()};oe.update(t=>[...t,a]),await te.saveData()},async updateItem(e,a){oe.update(t=>t.map(n=>n.id===e?{...n,...a}:n)),await te.saveData()},async deleteItem(e){oe.update(a=>a.filter(t=>t.id!==e)),await te.saveData()},async toggleTask(e){oe.update(a=>a.map(t=>t.id===e&&t.type==="task"?{...t,completed:!t.completed}:t)),await te.saveData()}};async function ut(){try{return typeof chrome<"u"&&chrome.tabs?(await chrome.tabs.query({active:!0,currentWindow:!0}))[0]||null:new Promise(e=>{chrome.runtime.sendMessage({type:"GET_CURRENT_TAB"},a=>{e(a?.tab||null)})})}catch(e){return console.error("Failed to get current tab:",e),null}}function Je(e){try{return new URL(e).hostname}catch(a){return console.error("Failed to extract domain from URL:",e,a),null}}function ga(){return typeof chrome<"u"&&chrome.runtime&&chrome.runtime.id}function ft(e){return e?e.replace(/^www\./,""):"Global"}function ya(e){ga()&&chrome.runtime.onMessage.addListener(a=>{(a.type==="TAB_CHANGED"||a.type==="URL_CHANGED")&&ut().then(e)})}var ka=E('<div class="modal-backdrop svelte-1ihqi4b" role="dialog" aria-modal="true" aria-labelledby="modal-title" tabindex="-1"><div><div class="modal-header svelte-1ihqi4b"><h2 id="modal-title" class="svelte-1ihqi4b"> </h2> <button class="close-button svelte-1ihqi4b" aria-label="Close modal">×</button></div> <div class="modal-body svelte-1ihqi4b"><!></div></div></div>');function Qe(e,a){Me(a,!1);let t=he(a,"isOpen",8,!1),n=he(a,"title",8,""),s=he(a,"size",8,"medium");const r=je();function l(){r("close")}function c(v){v.key==="Escape"&&l()}function g(v){v.target===v.currentTarget&&l()}Ne();var k=At();I("keydown",ea,c);var p=$e(k);{var w=v=>{var _=ka(),y=d(_),O=d(y),$=d(O),b=d($,!0);i($);var C=m($,2);i(O);var G=m(O,2),M=d(G);ca(M,a,"default",{}),i(G),i(y),i(_),z(()=>{ee(y,1,`modal-content ${s()??""}`,"svelte-1ihqi4b"),S(b,n())}),I("click",C,l),I("click",_,g),I("keydown",_,c),T(v,_)};N(p,v=>{t()&&v(w)})}T(e,k),Le()}var wa=E('<div class="form-group svelte-s3dxba"><label for="dueDate" class="svelte-s3dxba">Due Date</label> <input id="dueDate" type="date" class="svelte-s3dxba"/></div>'),xa=E('<p class="context-info svelte-s3dxba">This will be saved for: <strong> </strong></p>'),Da=E('<form class="item-form svelte-s3dxba"><div class="form-group svelte-s3dxba"><label for="content" class="svelte-s3dxba"> </label> <input id="content" type="text" required class="svelte-s3dxba"/></div> <div class="form-group svelte-s3dxba"><label for="details" class="svelte-s3dxba">Details</label> <textarea id="details" placeholder="Additional details..." rows="3" class="svelte-s3dxba"></textarea></div> <!> <div class="form-group svelte-s3dxba"><label for="tags" class="svelte-s3dxba">Tags</label> <input id="tags" type="text" placeholder="work, urgent, research (comma-separated)" class="svelte-s3dxba"/></div> <div class="form-group svelte-s3dxba"><label class="checkbox-label svelte-s3dxba"><input type="checkbox" class="svelte-s3dxba"/> <span class="checkmark"></span> Global (not specific to current website)</label> <!></div> <div class="form-actions svelte-s3dxba"><button type="button" class="btn btn-secondary svelte-s3dxba">Cancel</button> <button type="submit" class="btn btn-primary svelte-s3dxba"> </button></div></form>');function Ta(e,a){Me(a,!1);const[t,n]=ct(),s=()=>ve(pe,"$currentDomain",t),r=re();let l=he(a,"item",8,null),c=he(a,"type",8,"note");const g=je();let k=re(l()?.content||""),p=re(l()?.details||""),w=re(l()?.tags.join(", ")||""),v=re(l()?.domain===null||!1),_=re(l()?.type==="task"&&l().dueDate?l().dueDate.split("T")[0]:"");function y(){if(!A(k).trim())return;const u=A(w).split(",").map(x=>x.trim()).filter(x=>x.length>0),h={type:c(),content:A(k).trim(),details:A(p).trim()||void 0,domain:A(v)?null:s(),tags:u,...c()==="task"&&{completed:l()?.type==="task"?l().completed:!1,dueDate:A(_)||null}};A(r)&&l()?g("update",{id:l().id,...h}):g("create",h)}function O(){g("cancel")}lt(()=>ta(l()),()=>{ce(r,l()!==null)}),ot(),Ne();var $=Da(),b=d($),C=d(b),G=d(C);i(C);var M=m(C,2);ke(M),i(b);var L=m(b,2),U=m(d(L),2);na(U),i(L);var R=m(L,2);{var X=u=>{var h=wa(),x=m(d(h),2);ke(x),i(h),we(x,()=>A(_),V=>ce(_,V)),T(u,h)};N(R,u=>{c()==="task"&&u(X)})}var B=m(R,2),q=m(d(B),2);ke(q),i(B);var F=m(B,2),Y=d(F),ae=d(Y);ke(ae),aa(3),i(Y);var _e=m(Y,2);{var be=u=>{var h=xa(),x=m(d(h)),V=d(x,!0);i(x),i(h),z(()=>S(V,s())),T(u,h)};N(_e,u=>{!A(v)&&s()&&u(be)})}i(F);var Q=m(F,2),se=d(Q),Z=m(se,2),ge=d(Z);i(Z),i(Q),i($),z(u=>{S(G,`${c()==="task"?"Task":"Note"} Content *`),de(M,"placeholder",c()==="task"?"What needs to be done?":"What do you want to remember?"),Z.disabled=u,S(ge,`${A(r)?"Update":"Create"} ${c()==="task"?"Task":"Note"}`)},[()=>!A(k).trim()],K),we(M,()=>A(k),u=>ce(k,u)),we(U,()=>A(p),u=>ce(p,u)),we(q,()=>A(w),u=>ce(w,u)),pa(ae,()=>A(v),u=>ce(v,u)),I("click",se,O),I("submit",$,ma(y)),T(e,$),Le(),n()}var Ca=Et('<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"></path></svg>'),Aa=E("<button><!></button>"),Ea=E('<div class="note-icon svelte-183j7as"><svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"></path></svg></div>'),$a=E('<p class="item-details svelte-183j7as"> </p>'),Ia=E("<span> </span>"),Ma=E('<span class="tag svelte-183j7as"> </span>'),La=E('<div class="tags svelte-183j7as"></div>'),Na=E('<div><div class="item-header svelte-183j7as"><!> <div class="item-content svelte-183j7as"><h3 class="item-title svelte-183j7as"> </h3> <!></div> <div class="item-actions svelte-183j7as"><button class="action-btn edit-btn svelte-183j7as"><svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor"><path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708L10.5 8.207l-3-3L12.146.146zM11.207 9l-3-3L2.5 11.707V14.5a.5.5 0 0 0 .5.5h2.793L11.207 9z"></path></svg></button> <button class="action-btn delete-btn svelte-183j7as"><svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor"><path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"></path><path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"></path></svg></button></div></div> <div class="item-meta svelte-183j7as"><div class="meta-left svelte-183j7as"><span class="domain-tag svelte-183j7as"> </span> <!> <!></div> <div class="meta-right"><span class="created-date svelte-183j7as"> </span></div></div></div>');function Sa(e,a){Me(a,!1);let t=he(a,"item",8);const n=je();function s(){t().type==="task"&&n("toggle",t().id)}function r(){n("edit",t())}function l(){n("delete",t())}function c(u){const h=new Date(u),x=new Date().getFullYear();return h.toLocaleDateString("en-US",{month:"short",day:"numeric",...h.getFullYear()!==x&&{year:"numeric"}})}function g(u){return new Date(u).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit"})}function k(u){const h=new Date(u),x=new Date,V=Math.ceil((h.getTime()-x.getTime())/(1e3*60*60*24));return V<=3&&V>=0}function p(u){return new Date(u)<new Date}Ne();var w=Na();let v;var _=d(w),y=d(_);{var O=u=>{var h=Aa();let x;var V=d(h);{var P=j=>{var De=Ca();T(j,De)};N(V,j=>{t().completed&&j(P)})}i(h),z(j=>{x=ee(h,1,"task-checkbox svelte-183j7as",null,x,j),de(h,"aria-label",t().completed?"Mark as incomplete":"Mark as complete")},[()=>({checked:t().completed})],K),I("click",h,s),T(u,h)},$=u=>{var h=Ea();T(u,h)};N(y,u=>{t().type==="task"?u(O):u($,!1)})}var b=m(y,2),C=d(b),G=d(C,!0);i(C);var M=m(C,2);{var L=u=>{var h=$a(),x=d(h,!0);i(h),z(()=>S(x,t().details)),T(u,h)};N(M,u=>{t().details&&u(L)})}i(b);var U=m(b,2),R=d(U),X=m(R,2);i(U),i(_);var B=m(_,2),q=d(B),F=d(q),Y=d(F,!0);i(F);var ae=m(F,2);{var _e=u=>{var h=Ia();let x;var V=d(h);i(h),z((P,j)=>{x=ee(h,1,"due-date svelte-183j7as",null,x,P),S(V,`Due: ${j??""}`)},[()=>({"due-soon":k(t().dueDate),overdue:p(t().dueDate)}),()=>c(t().dueDate)],K),T(u,h)};N(ae,u=>{t().type==="task"&&t().dueDate&&u(_e)})}var be=m(ae,2);{var Q=u=>{var h=La();vt(h,5,()=>t().tags,ra,(x,V)=>{var P=Ma(),j=d(P,!0);i(P),z(()=>S(j,A(V))),T(x,P)}),i(h),T(u,h)};N(be,u=>{t().tags.length>0&&u(Q)})}i(q);var se=m(q,2),Z=d(se),ge=d(Z);i(Z),i(se),i(B),i(w),z((u,h,x,V,P)=>{v=ee(w,1,`item-card ${t().type??""}`,"svelte-183j7as",v,u),S(G,t().content),de(R,"aria-label",`Edit ${t().type??""}`),de(X,"aria-label",`Delete ${t().type??""}`),S(Y,h),de(Z,"title",x),S(ge,`${V??""} at ${P??""}`)},[()=>({completed:t().type==="task"&&t().completed}),()=>ft(t().domain),()=>new Date(t().createdAt).toLocaleString(),()=>c(t().createdAt),()=>g(t().createdAt)],K),I("click",R,r),I("click",X,l),T(e,w),Le()}var Oa=E('<p class="current-site svelte-1u8h1ny">Current site: <strong> </strong></p>'),Va=E('<span class="count svelte-1u8h1ny"> </span>'),za=E('<span class="count svelte-1u8h1ny"> </span>'),Ga=E('<span class="count svelte-1u8h1ny"> </span>'),Ha=E('<span class="count svelte-1u8h1ny"> </span>'),ja=E('<button class="add-btn svelte-1u8h1ny">+ Add Task</button>'),Ua=E('<button class="add-btn svelte-1u8h1ny">+ Add Note</button>'),Ra=E('<p class="svelte-1u8h1ny">No active tasks found.</p> <button class="add-btn svelte-1u8h1ny">Create your first task</button>',1),Ba=E('<p class="svelte-1u8h1ny">No notes found.</p> <button class="add-btn svelte-1u8h1ny">Create your first note</button>',1),qa=E('<p class="svelte-1u8h1ny">No completed tasks found.</p>'),Fa=E('<div class="empty-state svelte-1u8h1ny"><!></div>'),Ya=E('<div class="items-list svelte-1u8h1ny"></div>'),Pa=E('<div class="delete-confirm svelte-1u8h1ny"><p class="svelte-1u8h1ny"> </p> <p class="item-preview svelte-1u8h1ny"> </p> <div class="confirm-actions svelte-1u8h1ny"><button class="btn btn-secondary svelte-1u8h1ny">Cancel</button> <button class="btn btn-danger svelte-1u8h1ny">Delete</button></div></div>'),Wa=E('<div class="app svelte-1u8h1ny"><header class="app-header svelte-1u8h1ny"><h1 class="svelte-1u8h1ny">Website Companion</h1> <!></header> <div class="search-section svelte-1u8h1ny"><input type="text" placeholder="Search notes and tasks..." class="search-input svelte-1u8h1ny"/></div> <div class="filters svelte-1u8h1ny"><button>Current Site <!></button> <button>Global <!></button></div> <nav class="tabs svelte-1u8h1ny"><button>Active Tasks <!></button> <button>Notes</button> <button>Completed <!></button></nav> <div class="action-buttons svelte-1u8h1ny"><!></div> <main class="content svelte-1u8h1ny"><!></main></div> <!> <!>',1);function as(e,a){Me(a,!1);const[t,n]=ct(),s=()=>ve(Ee,"$modalState",t),r=()=>ve(pe,"$currentDomain",t),l=()=>ve(Ie,"$filterState",t),c=()=>ve(ba,"$taskCounts",t),g=()=>ve(xe,"$activeTab",t),k=()=>ve(_a,"$filteredItems",t);let p=re("");It(async()=>{await te.loadData();const o=await ut();o?.url&&(Xe.set(o.url),pe.set(Je(o.url))),ya(async f=>{f?.url&&(Xe.set(f.url),pe.set(Je(f.url)))})});function w(o,f=null){Ee.set({isOpen:!0,type:o,item:f})}function v(){Ee.set({isOpen:!1,type:null,item:null})}async function _(o){await te.addItem(o.detail),v()}async function y(o){const{id:f,...D}=o.detail;await te.updateItem(f,D),v()}async function O(o){await te.toggleTask(o.detail)}function $(o){const f=o.detail,D=f.type==="task"?"edit-task":"edit-note";w(D,f)}function b(o){Ee.set({isOpen:!0,type:"delete-confirm",item:o.detail})}async function C(){s().item&&(await te.deleteItem(s().item.id),v())}function G(o){Ie.update(f=>({...f,[o==="global"?"showGlobal":"showContextual"]:!f[o==="global"?"showGlobal":"showContextual"]}))}lt(()=>A(p),()=>{Ie.update(o=>({...o,searchTerm:A(p)}))}),ot(),Ne();var M=Wa(),L=$e(M),U=d(L),R=m(d(U),2);{var X=o=>{var f=Oa(),D=m(d(f)),W=d(D,!0);i(D),i(f),z(ie=>S(W,ie),[()=>ft(r())],K),T(o,f)};N(R,o=>{r()&&o(X)})}i(U);var B=m(U,2),q=d(B);ke(q),i(B);var F=m(B,2),Y=d(F);let ae;var _e=m(d(Y));{var be=o=>{var f=Va(),D=d(f,!0);i(f),z(()=>S(D,c().contextual.active)),T(o,f)};N(_e,o=>{c().contextual.active>0&&o(be)})}i(Y);var Q=m(Y,2);let se;var Z=m(d(Q));{var ge=o=>{var f=za(),D=d(f,!0);i(f),z(()=>S(D,c().global.active)),T(o,f)};N(Z,o=>{c().global.active>0&&o(ge)})}i(Q),i(F);var u=m(F,2),h=d(u);let x;var V=m(d(h));{var P=o=>{var f=Ga(),D=d(f,!0);i(f),z(()=>S(D,c().contextual.active+c().global.active)),T(o,f)};N(V,o=>{c().contextual.active+c().global.active>0&&o(P)})}i(h);var j=m(h,2);let De;var Te=m(j,2);let Ue;var ht=m(d(Te));{var pt=o=>{var f=Ha(),D=d(f,!0);i(f),z(()=>S(D,c().contextual.completed+c().global.completed)),T(o,f)};N(ht,o=>{c().contextual.completed+c().global.completed>0&&o(pt)})}i(Te),i(u);var Se=m(u,2),mt=d(Se);{var _t=o=>{var f=ja();I("click",f,()=>w("add-task")),T(o,f)},bt=o=>{var f=Ua();I("click",f,()=>w("add-note")),T(o,f)};N(mt,o=>{g()==="tasks"||g()==="completed"?o(_t):o(bt,!1)})}i(Se);var Re=m(Se,2),gt=d(Re);{var yt=o=>{var f=Fa(),D=d(f);{var W=J=>{var ue=Ra(),fe=m($e(ue),2);I("click",fe,()=>w("add-task")),T(J,ue)},ie=(J,ue)=>{{var fe=ne=>{var Ae=Ba(),Ct=m($e(Ae),2);I("click",Ct,()=>w("add-note")),T(ne,Ae)},Ce=ne=>{var Ae=qa();T(ne,Ae)};N(J,ne=>{g()==="notes"?ne(fe):ne(Ce,!1)},ue)}};N(D,J=>{g()==="tasks"?J(W):J(ie,!1)})}i(f),T(o,f)},kt=o=>{var f=Ya();vt(f,5,k,D=>D.id,(D,W)=>{Sa(D,{get item(){return A(W)},$$events:{toggle:O,edit:$,delete:b}})}),i(f),T(o,f)};N(gt,o=>{k().length===0?o(yt):o(kt,!1)})}i(Re),i(L);var Be=m(L,2);const wt=K(()=>s().isOpen&&(s().type==="add-note"||s().type==="add-task"||s().type==="edit-note"||s().type==="edit-task")),xt=K(()=>s().type?.includes("add")?`Add ${s().type?.includes("task")?"Task":"Note"}`:`Edit ${s().type?.includes("task")?"Task":"Note"}`);Qe(Be,{get isOpen(){return A(wt)},get title(){return A(xt)},$$events:{close:v},children:(o,f)=>{const D=K(()=>s().type?.includes("task")?"task":"note");Ta(o,{get item(){return s().item},get type(){return A(D)},$$events:{create:_,update:y,cancel:v}})},$$slots:{default:!0}});var Dt=m(Be,2);const Tt=K(()=>s().isOpen&&s().type==="delete-confirm");Qe(Dt,{get isOpen(){return A(Tt)},title:"Confirm Delete",size:"small",$$events:{close:v},children:(o,f)=>{var D=Pa(),W=d(D),ie=d(W);i(W);var J=m(W,2),ue=d(J);i(J);var fe=m(J,2),Ce=d(fe),ne=m(Ce,2);i(fe),i(D),z(()=>{S(ie,`Are you sure you want to delete this ${s().item?.type??""}?`),S(ue,`"${s().item?.content??""}"`)}),I("click",Ce,v),I("click",ne,C),T(o,D)},$$slots:{default:!0}}),z((o,f,D,W,ie)=>{ae=ee(Y,1,"filter-btn svelte-1u8h1ny",null,ae,o),se=ee(Q,1,"filter-btn svelte-1u8h1ny",null,se,f),x=ee(h,1,"tab svelte-1u8h1ny",null,x,D),De=ee(j,1,"tab svelte-1u8h1ny",null,De,W),Ue=ee(Te,1,"tab svelte-1u8h1ny",null,Ue,ie)},[()=>({active:l().showContextual}),()=>({active:l().showGlobal}),()=>({active:g()==="tasks"}),()=>({active:g()==="notes"}),()=>({active:g()==="completed"})],K),we(q,()=>A(p),o=>ce(p,o)),I("click",Y,()=>G("contextual")),I("click",Q,()=>G("global")),I("click",h,()=>xe.set("tasks")),I("click",j,()=>xe.set("notes")),I("click",Te,()=>xe.set("completed")),T(e,M),Le(),n()}export{as as A};
