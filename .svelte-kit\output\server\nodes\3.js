

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/popup/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/3.Dzj-wvXy.js","_app/immutable/chunks/DzLgamHK.js","_app/immutable/chunks/BxdUnLNP.js","_app/immutable/chunks/CsYk2EZE.js","_app/immutable/chunks/R9Shl-co.js","_app/immutable/chunks/CDb_H_Tf.js","_app/immutable/chunks/8hOwRzcT.js","_app/immutable/chunks/s-9L0vgi.js","_app/immutable/chunks/D7JjSAwG.js"];
export const stylesheets = ["_app/immutable/assets/App.BRvRBMPF.css","_app/immutable/assets/3.V2J_20dn.css"];
export const fonts = [];
