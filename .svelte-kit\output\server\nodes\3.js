

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/popup/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/3.81liJh8l.js","_app/immutable/chunks/DpgcOpH8.js","_app/immutable/chunks/DqHgutWf.js","_app/immutable/chunks/C1sE6Cnk.js","_app/immutable/chunks/CBEuTvut.js","_app/immutable/chunks/Cw7DmGe3.js","_app/immutable/chunks/DEldJQSp.js","_app/immutable/chunks/CinGyS9S.js","_app/immutable/chunks/D-Tr_g7H.js"];
export const stylesheets = ["_app/immutable/assets/App.BRvRBMPF.css","_app/immutable/assets/3.V2J_20dn.css"];
export const fonts = [];
