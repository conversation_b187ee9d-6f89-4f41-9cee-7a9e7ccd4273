<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { Item } from '../types';
  import { formatDomain } from '../utils/chrome';
  
  export let item: Item;
  
  const dispatch = createEventDispatcher();
  
  function handleToggleTask() {
    if (item.type === 'task') {
      dispatch('toggle', item.id);
    }
  }
  
  function handleEdit() {
    dispatch('edit', item);
  }
  
  function handleDelete() {
    dispatch('delete', item);
  }
  
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    const currentYear = new Date().getFullYear();
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      ...(date.getFullYear() !== currentYear && { year: 'numeric' })
    });
  }
  
  function formatTime(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit'
    });
  }
  
  function isDueSoon(dueDate: string): boolean {
    const due = new Date(dueDate);
    const now = new Date();
    const diffDays = Math.ceil((due.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays <= 3 && diffDays >= 0;
  }
  
  function isOverdue(dueDate: string): boolean {
    const due = new Date(dueDate);
    const now = new Date();
    return due < now;
  }
</script>

<div class="item-card {item.type}" class:completed={item.type === 'task' && item.completed}>
  <div class="item-header">
    {#if item.type === 'task'}
      <button 
        class="task-checkbox"
        class:checked={item.completed}
        on:click={handleToggleTask}
        aria-label={item.completed ? 'Mark as incomplete' : 'Mark as complete'}
      >
        {#if item.completed}
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
          </svg>
        {/if}
      </button>
    {:else}
      <div class="note-icon">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"/>
        </svg>
      </div>
    {/if}
    
    <div class="item-content">
      <h3 class="item-title">{item.content}</h3>
      {#if item.details}
        <p class="item-details">{item.details}</p>
      {/if}
    </div>
    
    <div class="item-actions">
      <button 
        class="action-btn edit-btn"
        on:click={handleEdit}
        aria-label="Edit {item.type}"
      >
        <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
          <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708L10.5 8.207l-3-3L12.146.146zM11.207 9l-3-3L2.5 11.707V14.5a.5.5 0 0 0 .5.5h2.793L11.207 9z"/>
        </svg>
      </button>
      <button 
        class="action-btn delete-btn"
        on:click={handleDelete}
        aria-label="Delete {item.type}"
      >
        <svg width="14" height="14" viewBox="0 0 16 16" fill="currentColor">
          <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
          <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
        </svg>
      </button>
    </div>
  </div>
  
  <div class="item-meta">
    <div class="meta-left">
      <span class="domain-tag">
        {formatDomain(item.domain)}
      </span>
      
      {#if item.type === 'task' && item.dueDate}
        <span 
          class="due-date"
          class:due-soon={isDueSoon(item.dueDate)}
          class:overdue={isOverdue(item.dueDate)}
        >
          Due: {formatDate(item.dueDate)}
        </span>
      {/if}
      
      {#if item.tags.length > 0}
        <div class="tags">
          {#each item.tags as tag}
            <span class="tag">{tag}</span>
          {/each}
        </div>
      {/if}
    </div>
    
    <div class="meta-right">
      <span class="created-date" title={new Date(item.createdAt).toLocaleString()}>
        {formatDate(item.createdAt)} at {formatTime(item.createdAt)}
      </span>
    </div>
  </div>
</div>

<style>
  .item-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s;
  }
  
  .item-card:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .item-card.completed {
    opacity: 0.7;
    background: #f9fafb;
  }
  
  .item-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .task-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    flex-shrink: 0;
    margin-top: 2px;
  }
  
  .task-checkbox:hover {
    border-color: #3b82f6;
  }
  
  .task-checkbox.checked {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
  }
  
  .note-icon {
    width: 20px;
    height: 20px;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 2px;
  }
  
  .item-content {
    flex: 1;
    min-width: 0;
  }
  
  .item-title {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: #111827;
    line-height: 1.4;
    word-wrap: break-word;
  }
  
  .completed .item-title {
    text-decoration: line-through;
    color: #6b7280;
  }
  
  .item-details {
    margin: 0;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
    word-wrap: break-word;
  }
  
  .item-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.2s;
  }
  
  .action-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }
  
  .delete-btn:hover {
    background: #fef2f2;
    color: #dc2626;
  }
  
  .item-meta {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 12px;
    font-size: 12px;
  }
  
  .meta-left {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
  }
  
  .domain-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }
  
  .due-date {
    color: #6b7280;
    font-weight: 500;
  }
  
  .due-date.due-soon {
    color: #f59e0b;
  }
  
  .due-date.overdue {
    color: #dc2626;
  }
  
  .tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  .tag {
    background: #eff6ff;
    color: #1d4ed8;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
  }
  
  .created-date {
    color: #9ca3af;
    white-space: nowrap;
  }
</style>
