export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png"]),
	mimeTypes: {".png":"image/png"},
	_: {
		client: {start:"_app/immutable/entry/start.DfdAGYFX.js",app:"_app/immutable/entry/app.CR1oAOk_.js",imports:["_app/immutable/entry/start.DfdAGYFX.js","_app/immutable/chunks/DpCXCK4X.js","_app/immutable/chunks/BxdUnLNP.js","_app/immutable/chunks/8hOwRzcT.js","_app/immutable/entry/app.CR1oAOk_.js","_app/immutable/chunks/BxdUnLNP.js","_app/immutable/chunks/R9Shl-co.js","_app/immutable/chunks/DzLgamHK.js","_app/immutable/chunks/8hOwRzcT.js","_app/immutable/chunks/s-9L0vgi.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js')),
			__memo(() => import('./nodes/4.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/popup",
				pattern: /^\/popup\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			},
			{
				id: "/sidepanel",
				pattern: /^\/sidepanel\/?$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 4 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
