import{a2 as l,a3 as u,a4 as p,a5 as m,a6 as h,a7 as g,m as d,o,y as E,C as T}from"./BxdUnLNP.js";function v(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function a(e,t){var r=m;r.nodes_start===null&&(r.nodes_start=e,r.nodes_end=t)}function x(e,t){var r=(t&h)!==0,_=(t&g)!==0,n,f=!e.startsWith("<!>");return()=>{if(d)return a(o,null),o;n===void 0&&(n=v(f?e:"<!>"+e),r||(n=u(n)));var s=_||p?document.importNode(n,!0):n.cloneNode(!0);if(r){var c=u(s),i=s.lastChild;a(c,i)}else a(s,s);return s}}function y(e,t,r="svg"){var _=!e.startsWith("<!>"),n=`<${r}>${_?e:"<!>"+e}</${r}>`,f;return()=>{if(d)return a(o,null),o;if(!f){var s=v(n),c=u(s);f=u(c)}var i=f.cloneNode(!0);return a(i,i),i}}function M(e,t){return y(e,t,"svg")}function A(e=""){if(!d){var t=l(e+"");return a(t,t),t}var r=o;return r.nodeType!==3&&(r.before(r=l()),T(r)),a(r,r),r}function C(){if(d)return a(o,null),o;var e=document.createDocumentFragment(),t=document.createComment(""),r=l();return e.append(t,r),a(t,r),e}function L(e,t){if(d){m.nodes_end=o,E();return}e!==null&&e.before(t)}const w="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(w);export{L as a,a as b,C as c,M as d,x as f,A as t};
