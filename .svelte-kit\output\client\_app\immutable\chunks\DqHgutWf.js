var fe=Array.isArray,ue=Array.prototype.indexOf,Ze=Array.from,ze=Object.defineProperty,K=Object.getOwnPropertyDescriptor,oe=Object.getOwnPropertyDescriptors,_e=Object.prototype,ce=Array.prototype,Ct=Object.getPrototypeOf,kt=Object.isExtensible;const We=()=>{};function Xe(t){return t()}function Ft(t){for(var n=0;n<t.length;n++)t[n]()}const x=2,Mt=4,ft=8,mt=16,k=32,H=64,et=128,g=256,nt=512,y=1024,I=2048,N=4096,Y=8192,ut=16384,ve=32768,qt=65536,Je=1<<17,de=1<<19,Lt=1<<20,yt=1<<21,$=Symbol("$state"),Qe=Symbol("legacy props"),tn=Symbol("");function Yt(t){return t===this.v}function pe(t,n){return t!=t?n==n:t!==n||t!==null&&typeof t=="object"||typeof t=="function"}function jt(t){return!pe(t,this.v)}function he(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function ye(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function we(t){throw new Error("https://svelte.dev/e/effect_orphan")}function Ee(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function en(){throw new Error("https://svelte.dev/e/hydration_failed")}function nn(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function ge(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function me(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Te(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let ot=!1;function rn(){ot=!0}const an=1,ln=2,sn=16,fn=1,un=2,on=4,_n=8,cn=16,vn=1,dn=2,xe="[",Ae="[!",be="]",Tt={},w=Symbol(),pn="http://www.w3.org/1999/xhtml";let p=null;function Ot(t){p=t}function hn(t,n=!1,e){var r=p={p,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};ot&&!n&&(p.l={s:null,u:null,r1:[],r2:At(!1)}),Se(()=>{r.d=!0})}function yn(t){const n=p;if(n!==null){const f=n.e;if(f!==null){var e=d,r=v;n.e=null;try{for(var a=0;a<f.length;a++){var l=f[a];lt(l.effect),j(l.reaction),Zt(l.fn)}}finally{lt(e),j(r)}}p=n.p,n.m=!0}return{}}function _t(){return!ot||p!==null&&p.l===null}function q(t){if(typeof t!="object"||t===null||$ in t)return t;const n=Ct(t);if(n!==_e&&n!==ce)return t;var e=new Map,r=fe(t),a=O(0),l=v,f=u=>{var s=v;j(l);var i=u();return j(s),i};return r&&e.set("length",O(t.length)),new Proxy(t,{defineProperty(u,s,i){(!("value"in i)||i.configurable===!1||i.enumerable===!1||i.writable===!1)&&ge();var _=e.get(s);return _===void 0?(_=f(()=>O(i.value)),e.set(s,_)):R(_,f(()=>q(i.value))),!0},deleteProperty(u,s){var i=e.get(s);if(i===void 0)s in u&&(e.set(s,f(()=>O(w))),ht(a));else{if(r&&typeof s=="string"){var _=e.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&R(_,o)}R(i,w),ht(a)}return!0},get(u,s,i){if(s===$)return t;var _=e.get(s),o=s in u;if(_===void 0&&(!o||K(u,s)?.writable)&&(_=f(()=>O(q(o?u[s]:w))),e.set(s,_)),_!==void 0){var c=L(_);return c===w?void 0:c}return Reflect.get(u,s,i)},getOwnPropertyDescriptor(u,s){var i=Reflect.getOwnPropertyDescriptor(u,s);if(i&&"value"in i){var _=e.get(s);_&&(i.value=L(_))}else if(i===void 0){var o=e.get(s),c=o?.v;if(o!==void 0&&c!==w)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return i},has(u,s){if(s===$)return!0;var i=e.get(s),_=i!==void 0&&i.v!==w||Reflect.has(u,s);if(i!==void 0||d!==null&&(!_||K(u,s)?.writable)){i===void 0&&(i=f(()=>O(_?q(u[s]):w)),e.set(s,i));var o=L(i);if(o===w)return!1}return _},set(u,s,i,_){var o=e.get(s),c=s in u;if(r&&s==="length")for(var G=i;G<o.v;G+=1){var Q=e.get(G+"");Q!==void 0?R(Q,w):G in u&&(Q=f(()=>O(w)),e.set(G+"",Q))}o===void 0?(!c||K(u,s)?.writable)&&(o=f(()=>O(void 0)),R(o,f(()=>q(i))),e.set(s,o)):(c=o.v!==w,R(o,f(()=>q(i))));var Dt=Reflect.getOwnPropertyDescriptor(u,s);if(Dt?.set&&Dt.set.call(_,i),!c){if(r&&typeof s=="string"){var It=e.get("length"),pt=Number(s);Number.isInteger(pt)&&pt>=It.v&&R(It,pt+1)}ht(a)}return!0},ownKeys(u){L(a);var s=Reflect.ownKeys(u).filter(o=>{var c=e.get(o);return c===void 0||c.v!==w});for(var[i,_]of e)_.v!==w&&!(i in u)&&s.push(i);return s},setPrototypeOf(){me()}})}function ht(t,n=1){R(t,t.v+n)}function xt(t){var n=x|I,e=v!==null&&(v.f&x)!==0?v:null;return d===null||e!==null&&(e.f&g)!==0?n|=g:d.f|=Lt,{ctx:p,deps:null,effects:null,equals:Yt,f:n,fn:t,reactions:null,rv:0,v:null,wv:0,parent:e??d}}function wn(t){const n=xt(t);return ne(n),n}function En(t){const n=xt(t);return n.equals=jt,n}function Ht(t){var n=t.effects;if(n!==null){t.effects=null;for(var e=0;e<n.length;e+=1)F(n[e])}}function Re(t){for(var n=t.parent;n!==null;){if((n.f&x)===0)return n;n=n.parent}return null}function Bt(t){var n,e=d;lt(Re(t));try{Ht(t),n=se(t)}finally{lt(e)}return n}function Ut(t){var n=Bt(t);if(t.equals(n)||(t.v=n,t.wv=ae()),!U){var e=(S||(t.f&g)!==0)&&t.deps!==null?N:y;T(t,e)}}const z=new Map;function At(t,n){var e={f:0,v:t,reactions:null,equals:Yt,rv:0,wv:0};return e}function O(t,n){const e=At(t);return ne(e),e}function gn(t,n=!1){const e=At(t);return n||(e.equals=jt),ot&&p!==null&&p.l!==null&&(p.l.s??=[]).push(e),e}function R(t,n,e=!1){v!==null&&!b&&_t()&&(v.f&(x|mt))!==0&&!D?.includes(t)&&Te();let r=e?q(n):n;return De(t,r)}function De(t,n){if(!t.equals(n)){var e=t.v;U?z.set(t,n):z.set(t,e),t.v=n,(t.f&x)!==0&&((t.f&I)!==0&&Bt(t),T(t,(t.f&g)===0?y:N)),t.wv=ae(),Vt(t,I),_t()&&d!==null&&(d.f&y)!==0&&(d.f&(k|H))===0&&(m===null?Ye([t]):m.push(t))}return n}function Vt(t,n){var e=t.reactions;if(e!==null)for(var r=_t(),a=e.length,l=0;l<a;l++){var f=e[l],u=f.f;(u&I)===0&&(!r&&f===d||(T(f,n),(u&(y|g))!==0&&((u&x)!==0?Vt(f,N):dt(f))))}}function bt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let C=!1;function mn(t){C=t}let A;function W(t){if(t===null)throw bt(),Tt;return A=t}function Tn(){return W(M(A))}function xn(t){if(C){if(M(A)!==null)throw bt(),Tt;A=t}}function An(t=1){if(C){for(var n=t,e=A;n--;)e=M(e);A=e}}function bn(){for(var t=0,n=A;;){if(n.nodeType===8){var e=n.data;if(e===be){if(t===0)return n;t-=1}else(e===xe||e===Ae)&&(t+=1)}var r=M(n);n.remove(),n=r}}function Rn(t){if(!t||t.nodeType!==8)throw bt(),Tt;return t.data}var St,Ie,ke,Gt,Kt;function Dn(){if(St===void 0){St=window,Ie=document,ke=/Firefox/.test(navigator.userAgent);var t=Element.prototype,n=Node.prototype,e=Text.prototype;Gt=K(n,"firstChild").get,Kt=K(n,"nextSibling").get,kt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),kt(e)&&(e.__t=void 0)}}function wt(t=""){return document.createTextNode(t)}function Et(t){return Gt.call(t)}function M(t){return Kt.call(t)}function In(t,n){if(!C)return Et(t);var e=Et(A);if(e===null)e=A.appendChild(wt());else if(n&&e.nodeType!==3){var r=wt();return e?.before(r),W(r),r}return W(e),e}function kn(t,n){if(!C){var e=Et(t);return e instanceof Comment&&e.data===""?M(e):e}return A}function On(t,n=1,e=!1){let r=C?A:t;for(var a;n--;)a=r,r=M(r);if(!C)return r;var l=r?.nodeType;if(e&&l!==3){var f=wt();return r===null?a?.after(f):r.before(f),W(f),f}return W(r),r}function Sn(t){t.textContent=""}function $t(t){d===null&&v===null&&we(),v!==null&&(v.f&g)!==0&&d===null&&ye(),U&&he()}function Oe(t,n){var e=n.last;e===null?n.last=n.first=t:(e.next=t,t.prev=e,n.last=t)}function B(t,n,e,r=!0){var a=d,l={ctx:p,deps:null,nodes_start:null,nodes_end:null,f:t|I,first:null,fn:n,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(e)try{vt(l),l.f|=ve}catch(s){throw F(l),s}else n!==null&&dt(l);var f=e&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(Lt|et))===0;if(!f&&r&&(a!==null&&Oe(l,a),v!==null&&(v.f&x)!==0)){var u=v;(u.effects??=[]).push(l)}return l}function Se(t){const n=B(ft,null,!1);return T(n,y),n.teardown=t,n}function Nn(t){$t();var n=d!==null&&(d.f&k)!==0&&p!==null&&!p.m;if(n){var e=p;(e.e??=[]).push({fn:t,effect:d,reaction:v})}else{var r=Zt(t);return r}}function Pn(t){return $t(),Rt(t)}function Cn(t){const n=B(H,t,!0);return(e={})=>new Promise(r=>{e.outro?Fe(n,()=>{F(n),r(void 0)}):(F(n),r(void 0))})}function Zt(t){return B(Mt,t,!1)}function Fn(t,n){var e=p,r={effect:null,ran:!1};e.l.r1.push(r),r.effect=Rt(()=>{t(),!r.ran&&(r.ran=!0,R(e.l.r2,!0),Ke(n))})}function Mn(){var t=p;Rt(()=>{if(L(t.l.r2)){for(var n of t.l.r1){var e=n.effect;(e.f&y)!==0&&T(e,N),V(e)&&vt(e),n.ran=!1}t.l.r2.v=!1}})}function Rt(t){return B(ft,t,!0)}function qn(t,n=[],e=xt){const r=n.map(e);return Ne(()=>t(...r.map(L)))}function Ne(t,n=0){return B(ft|mt|n,t,!0)}function Ln(t,n=!0){return B(ft|k,t,!0,n)}function zt(t){var n=t.teardown;if(n!==null){const e=U,r=v;Nt(!0),j(null);try{n.call(null)}finally{Nt(e),j(r)}}}function Wt(t,n=!1){var e=t.first;for(t.first=t.last=null;e!==null;){var r=e.next;(e.f&H)!==0?e.parent=null:F(e,n),e=r}}function Pe(t){for(var n=t.first;n!==null;){var e=n.next;(n.f&k)===0&&F(n),n=e}}function F(t,n=!0){var e=!1;(n||(t.f&de)!==0)&&t.nodes_start!==null&&(Ce(t.nodes_start,t.nodes_end),e=!0),Wt(t,n&&!e),it(t,0),T(t,ut);var r=t.transitions;if(r!==null)for(const l of r)l.stop();zt(t);var a=t.parent;a!==null&&a.first!==null&&Xt(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Ce(t,n){for(;t!==null;){var e=t===n?null:M(t);t.remove(),t=e}}function Xt(t){var n=t.parent,e=t.prev,r=t.next;e!==null&&(e.next=r),r!==null&&(r.prev=e),n!==null&&(n.first===t&&(n.first=r),n.last===t&&(n.last=e))}function Fe(t,n){var e=[];Jt(t,e,!0),Me(e,()=>{F(t),n&&n()})}function Me(t,n){var e=t.length;if(e>0){var r=()=>--e||n();for(var a of t)a.out(r)}else n()}function Jt(t,n,e){if((t.f&Y)===0){if(t.f^=Y,t.transitions!==null)for(const f of t.transitions)(f.is_global||e)&&n.push(f);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&qt)!==0||(r.f&k)!==0;Jt(r,n,l?e:!1),r=a}}}function Yn(t){Qt(t,!0)}function Qt(t,n){if((t.f&Y)!==0){t.f^=Y,(t.f&y)===0&&(t.f^=y),V(t)&&(T(t,I),dt(t));for(var e=t.first;e!==null;){var r=e.next,a=(e.f&qt)!==0||(e.f&k)!==0;Qt(e,a?n:!1),e=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||n)&&l.in()}}const qe=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let X=[],J=[];function te(){var t=X;X=[],Ft(t)}function ee(){var t=J;J=[],Ft(t)}function jn(t){X.length===0&&queueMicrotask(te),X.push(t)}function Hn(t){J.length===0&&qe(ee),J.push(t)}function Le(){X.length>0&&te(),J.length>0&&ee()}let tt=!1,rt=!1,at=null,P=!1,U=!1;function Nt(t){U=t}let Z=[];let v=null,b=!1;function j(t){v=t}let d=null;function lt(t){d=t}let D=null;function ne(t){v!==null&&v.f&yt&&(D===null?D=[t]:D.push(t))}let h=null,E=0,m=null;function Ye(t){m=t}let re=1,st=0,S=!1;function ae(){return++re}function V(t){var n=t.f;if((n&I)!==0)return!0;if((n&N)!==0){var e=t.deps,r=(n&g)!==0;if(e!==null){var a,l,f=(n&nt)!==0,u=r&&d!==null&&!S,s=e.length;if(f||u){var i=t,_=i.parent;for(a=0;a<s;a++)l=e[a],(f||!l?.reactions?.includes(i))&&(l.reactions??=[]).push(i);f&&(i.f^=nt),u&&_!==null&&(_.f&g)===0&&(i.f^=g)}for(a=0;a<s;a++)if(l=e[a],V(l)&&Ut(l),l.wv>t.wv)return!0}(!r||d!==null&&!S)&&T(t,y)}return!1}function je(t,n){for(var e=n;e!==null;){if((e.f&et)!==0)try{e.fn(t);return}catch{e.f^=et}e=e.parent}throw tt=!1,t}function Pt(t){return(t.f&ut)===0&&(t.parent===null||(t.parent.f&et)===0)}function ct(t,n,e,r){if(tt){if(e===null&&(tt=!1),Pt(n))throw t;return}if(e!==null&&(tt=!0),je(t,n),Pt(n))throw t}function le(t,n,e=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];D?.includes(t)||((l.f&x)!==0?le(l,n,!1):n===l&&(e?T(l,I):(l.f&y)!==0&&T(l,N),dt(l)))}}function se(t){var n=h,e=E,r=m,a=v,l=S,f=D,u=p,s=b,i=t.f;h=null,E=0,m=null,S=(i&g)!==0&&(b||!P||v===null),v=(i&(k|H))===0?t:null,D=null,Ot(t.ctx),b=!1,st++,t.f|=yt;try{var _=(0,t.fn)(),o=t.deps;if(h!==null){var c;if(it(t,E),o!==null&&E>0)for(o.length=E+h.length,c=0;c<h.length;c++)o[E+c]=h[c];else t.deps=o=h;if(!S)for(c=E;c<o.length;c++)(o[c].reactions??=[]).push(t)}else o!==null&&E<o.length&&(it(t,E),o.length=E);if(_t()&&m!==null&&!b&&o!==null&&(t.f&(x|N|I))===0)for(c=0;c<m.length;c++)le(m[c],t);return a!==null&&a!==t&&(st++,m!==null&&(r===null?r=m:r.push(...m))),_}finally{h=n,E=e,m=r,v=a,S=l,D=f,Ot(u),b=s,t.f^=yt}}function He(t,n){let e=n.reactions;if(e!==null){var r=ue.call(e,t);if(r!==-1){var a=e.length-1;a===0?e=n.reactions=null:(e[r]=e[a],e.pop())}}e===null&&(n.f&x)!==0&&(h===null||!h.includes(n))&&(T(n,N),(n.f&(g|nt))===0&&(n.f^=nt),Ht(n),it(n,0))}function it(t,n){var e=t.deps;if(e!==null)for(var r=n;r<e.length;r++)He(t,e[r])}function vt(t){var n=t.f;if((n&ut)===0){T(t,y);var e=d,r=p,a=P;d=t,P=!0;try{(n&mt)!==0?Pe(t):Wt(t),zt(t);var l=se(t);t.teardown=typeof l=="function"?l:null,t.wv=re;var f=t.deps,u}catch(s){ct(s,t,e,r||t.ctx)}finally{P=a,d=e}}}function Be(){try{Ee()}catch(t){if(at!==null)ct(t,at,null);else throw t}}function ie(){var t=P;try{var n=0;for(P=!0;Z.length>0;){n++>1e3&&Be();var e=Z,r=e.length;Z=[];for(var a=0;a<r;a++){var l=Ve(e[a]);Ue(l)}z.clear()}}finally{rt=!1,P=t,at=null}}function Ue(t){var n=t.length;if(n!==0)for(var e=0;e<n;e++){var r=t[e];if((r.f&(ut|Y))===0)try{V(r)&&(vt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Xt(r):r.fn=null))}catch(a){ct(a,r,null,r.ctx)}}}function dt(t){rt||(rt=!0,queueMicrotask(ie));for(var n=at=t;n.parent!==null;){n=n.parent;var e=n.f;if((e&(H|k))!==0){if((e&y)===0)return;n.f^=y}}Z.push(n)}function Ve(t){for(var n=[],e=t;e!==null;){var r=e.f,a=(r&(k|H))!==0,l=a&&(r&y)!==0;if(!l&&(r&Y)===0){if((r&Mt)!==0)n.push(e);else if(a)e.f^=y;else try{V(e)&&vt(e)}catch(s){ct(s,e,null,e.ctx)}var f=e.first;if(f!==null){e=f;continue}}var u=e.parent;for(e=e.next;e===null&&u!==null;)e=u.next,u=u.parent}return n}function Ge(t){for(var n;;){if(Le(),Z.length===0)return n;rt=!0,ie()}}async function Bn(){await Promise.resolve(),Ge()}function L(t){var n=t.f,e=(n&x)!==0;if(v!==null&&!b){if(!D?.includes(t)){var r=v.deps;t.rv<st&&(t.rv=st,h===null&&r!==null&&r[E]===t?E++:h===null?h=[t]:(!S||!h.includes(t))&&h.push(t))}}else if(e&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&(l.f&g)===0&&(a.f^=g)}return e&&(a=t,V(a)&&Ut(a)),U&&z.has(t)?z.get(t):t.v}function Ke(t){var n=b;try{return b=!0,t()}finally{b=n}}const $e=-7169;function T(t,n){t.f=t.f&$e|n}function Un(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if($ in t)gt(t);else if(!Array.isArray(t))for(let n in t){const e=t[n];typeof e=="object"&&e&&$ in e&&gt(e)}}}function gt(t,n=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!n.has(t)){n.add(t),t instanceof Date&&t.getTime();for(let r in t)try{gt(t[r],n)}catch{}const e=Ct(t);if(e!==Object.prototype&&e!==Array.prototype&&e!==Map.prototype&&e!==Set.prototype&&e!==Date.prototype){const r=oe(e);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{Ie as $,Ae as A,bn as B,W as C,mn as D,qt as E,Yn as F,Fe as G,xe as H,gn as I,Se as J,ze as K,R as L,K as M,nn as N,Je as O,En as P,Qe as Q,ot as R,$ as S,un as T,w as U,_n as V,on as W,jt as X,q as Y,cn as Z,fn as _,Nn as a,pe as a0,fe as a1,wt as a2,Et as a3,ke as a4,d as a5,vn as a6,dn as a7,Sn as a8,j as a9,Hn as aA,pn as aB,Ct as aC,tn as aD,oe as aE,_t as aF,St as aG,Fn as aH,Mn as aI,lt as aa,v as ab,jn as ac,de as ad,M as ae,Dn as af,Tt as ag,be as ah,bt as ai,en as aj,Ze as ak,Cn as al,Zt as am,Rt as an,Ge as ao,O as ap,Bn as aq,wn as ar,At as as,Y as at,ln as au,De as av,Jt as aw,Me as ax,an as ay,sn as az,Ke as b,p as c,Xe as d,Un as e,xt as f,L as g,rn as h,Ne as i,Ln as j,We as k,F as l,C as m,An as n,A as o,kn as p,hn as q,Ft as r,yn as s,qn as t,Pn as u,In as v,xn as w,On as x,Tn as y,Rn as z};
