{"name": "contextual-website-companion", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev", "build": "bun run build:extension", "build:extension": "mkdir -p build && bun run copy:manifest && bun run copy:background && bun run copy:static", "copy:manifest": "cp manifest.json build/", "copy:background": "cp src/background.js build/", "copy:static": "cp static/* build/", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/chrome": "^0.0.326", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"uuid": "^11.1.0"}}