{"name": "contextual-website-companion", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && bun run build:extension", "build:extension": "bun run copy:manifest && bun run copy:background && bun run copy:html", "copy:manifest": "cp manifest.json build/", "copy:background": "cp src/background.js build/", "copy:html": "cp build/popup.html build/popup.html.bak 2>/dev/null || true && cp build/sidepanel.html build/sidepanel.html.bak 2>/dev/null || true", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/chrome": "^0.0.326", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"uuid": "^11.1.0"}}