import{c as s,a as i}from"../chunks/DpgcOpH8.js";import{i as c,E as p,j as f,k as l,l as d,m as u,o as m,p as _}from"../chunks/DqHgutWf.js";function v(t,n,...e){var r=t,o=l,a;c(()=>{o!==(o=n())&&(a&&(d(a),a=null),a=f(()=>o(r,...e)))},p),u&&(r=m)}const y=!0,h=!1,E=Object.freeze(Object.defineProperty({__proto__:null,prerender:y,ssr:h},Symbol.toStringTag,{value:"Module"}));function T(t,n){var e=s(),r=_(e);v(r,()=>n.children),i(t,e)}export{T as component,E as universal};
