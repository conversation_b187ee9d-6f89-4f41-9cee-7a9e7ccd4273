# Contextual Website Companion

A Chrome extension built with SvelteKit that allows users to manage notes and tasks specific to the website they are currently viewing, as well as global notes/tasks.

## Features

### Core Functionality
- **Contextual Notes & Tasks**: Create notes and tasks that are automatically associated with the current website domain
- **Global Notes & Tasks**: Create notes and tasks that are available across all websites
- **Chrome Side Panel**: Primary interface using Chrome's Side Panel API for persistent access
- **Popup Interface**: Alternative quick access via extension icon
- **Real-time Reactivity**: Instant UI updates using Svelte's reactivity system

### Advanced Features
- **Tabbed Interface**: Organized content with "Active Tasks," "Notes," and "Completed" tabs
- **Modal-based Actions**: Clean modal interfaces for adding, editing, and deleting items
- **Search & Filter**: Search across notes and tasks, filter by context (current site vs global)
- **Task Management**: Mark tasks as complete/incomplete, set due dates, track overdue items
- **Tagging System**: Add tags to organize notes and tasks
- **Persistent Storage**: All data saved using Chrome's storage API

## Technology Stack

- **SvelteKit**: Modern web framework with excellent reactivity
- **TypeScript**: Type-safe development
- **Chrome Extension APIs**: Storage, Side Panel, Active Tab
- **Bun**: Fast JavaScript runtime and package manager
- **Vite**: Build tool optimized for modern web development

## Installation & Development

### Prerequisites
- Bun installed on your system
- Chrome browser

### Setup
1. Clone or download this project
2. Install dependencies:
   ```bash
   bun install
   ```

3. Build the extension:
   ```bash
   bun run build
   ```

4. Load the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `build` folder
   - The extension should now appear in your extensions list

### Development
- Run `bun run dev` for development server (for testing components)
- Run `bun run build` to build the extension for Chrome
- Run `bun run check` to run TypeScript checks

## Usage

### Opening the Extension
1. **Side Panel** (Recommended): Click the extension icon in the toolbar to open the side panel
2. **Popup**: Right-click the extension icon and select the popup option

### Managing Notes and Tasks
1. **Create**: Click the "Add Task" or "Add Note" button
2. **Edit**: Click the edit icon on any item
3. **Delete**: Click the delete icon and confirm
4. **Complete Tasks**: Click the checkbox next to tasks to mark them complete

### Context Switching
- The extension automatically detects the current website
- Notes and tasks are filtered based on the current domain
- Use the filter buttons to show/hide global vs contextual items

### Search and Organization
- Use the search bar to find specific notes or tasks
- Add tags when creating items for better organization
- Set due dates for tasks to track deadlines

## Key Features Implementation

### Svelte Reactivity
- Uses Svelte stores for centralized state management
- Derived stores for computed values (filtered items, task counts)
- Automatic UI updates when data changes

### Chrome Extension Integration
- Background script handles tab changes and extension lifecycle
- Side Panel API for persistent interface
- Chrome storage for data persistence
- Active tab detection for contextual features

### Modern UI Patterns
- Modal-based workflows for complex actions
- Tabbed interface for content organization
- Responsive design that works in both popup and side panel
- Accessibility features (keyboard navigation, ARIA labels)

## Project Structure

```
src/
├── lib/
│   ├── components/          # Reusable Svelte components
│   │   ├── App.svelte      # Main application component
│   │   ├── Modal.svelte    # Reusable modal component
│   │   ├── ItemForm.svelte # Form for creating/editing items
│   │   └── ItemCard.svelte # Display component for notes/tasks
│   ├── stores/             # Svelte stores for state management
│   │   └── index.ts        # Main stores and actions
│   ├── types.ts            # TypeScript type definitions
│   └── utils/
│       └── chrome.ts       # Chrome API utility functions
├── routes/
│   ├── popup/              # Popup interface route
│   ├── sidepanel/          # Side panel interface route
│   └── +layout.ts          # Layout configuration
├── background.js           # Chrome extension background script
└── app.html               # Base HTML template

manifest.json              # Chrome extension manifest
```

## Contributing

This project demonstrates modern web development practices with SvelteKit and Chrome extension APIs. Feel free to extend it with additional features like:

- Data export/import
- Sync across devices
- Rich text editing
- Reminder notifications
- Integration with external services

## License

This project is provided as an educational example for building Chrome extensions with SvelteKit.
